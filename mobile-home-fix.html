<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>移动端首页布局修复演示</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        min-height: 100vh;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      .container {
        max-width: 1200px;
        padding: 30px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgb(0 0 0 / 12%);
      }
      .title {
        margin-bottom: 30px;
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .fix-summary {
        padding: 20px;
        margin-bottom: 30px;
        background: #f0f9ff;
        border-left: 4px solid #0ea5e9;
        border-radius: 8px;
      }
      .fix-summary h3 {
        margin-bottom: 15px;
        font-size: 18px;
        color: #0f172a;
      }
      .fix-item {
        display: flex;
        align-items: flex-start;
        padding: 8px 0;
        margin-bottom: 12px;
      }
      .fix-item::before {
        margin-top: 2px;
        margin-right: 10px;
        font-size: 16px;
        content: "🔧";
      }
      .device-preview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
      }
      .device {
        position: relative;
        padding: 20px;
        background: #1a1a1a;
        border-radius: 30px;
        box-shadow: 0 20px 40px rgb(0 0 0 / 30%);
      }
      .device::before {
        position: absolute;
        top: 8px;
        left: 50%;
        width: 50px;
        height: 4px;
        content: "";
        background: #444444;
        border-radius: 2px;
        transform: translateX(-50%);
      }
      .device-screen {
        position: relative;
        min-height: 600px;
        padding: 16px;
        overflow: hidden;
        background: #f8fafc;
        border-radius: 20px;
      }
      .device-title {
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 600;
        color: #666666;
        text-align: center;
      }

      /* 模拟首页布局 */
      .home-layout {
        display: grid;
        gap: 16px;
        height: 100%;
      }

      /* 桌面端：两列布局 */
      .desktop .home-layout {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }

      /* 移动端：单列布局 */
      .mobile .home-layout {
        grid-template-columns: 1fr;
        gap: 16px;
      }
      .card {
        display: flex;
        flex-direction: column;
        background: rgb(255 255 255 / 90%);
        backdrop-filter: blur(10px);
        border: 1px solid rgb(255 255 255 / 30%);
        border-radius: 12px;
        box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
        transition: all 0.3s ease;
      }
      .card:hover {
        box-shadow: 0 8px 24px rgb(0 0 0 / 15%);
        transform: translateY(-2px);
      }
      .card-header {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
      }
      .card-title {
        position: relative;
        padding-left: 12px;
        font-size: 15px;
        font-weight: 600;
        color: #374151;
      }
      .card-title::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 3px;
        height: 14px;
        content: "";
        background: #3b82f6;
        border-radius: 2px;
        transform: translateY(-50%);
      }
      .card-action {
        padding: 6px 12px;
        font-size: 12px;
        color: white;
        cursor: pointer;
        background: #3b82f6;
        border: none;
        border-radius: 6px;
        transition: all 0.3s ease;
      }
      .card-action:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }
      .card-body {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      /* 图表模拟 */
      .chart-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 200px;
        overflow: hidden;
        font-weight: 600;
        color: white;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
      }
      .chart-container::before {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        content: "";
        background: linear-gradient(90deg, transparent, rgb(255 255 255 / 20%), transparent);
        animation: shimmer 2s infinite;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      /* 消息列表模拟 */
      .message-list {
        width: 100%;
        height: 200px;
        overflow-y: auto;
      }
      .message-item {
        padding: 12px;
        margin-bottom: 8px;
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        transition: all 0.3s ease;
      }
      .message-item:hover {
        background: #f1f5f9;
        border-color: #3b82f6;
        transform: translateX(4px);
      }
      .message-title {
        margin-bottom: 4px;
        font-size: 13px;
        font-weight: 600;
        color: #374151;
      }
      .message-content {
        font-size: 12px;
        line-height: 1.4;
        color: #6b7280;
      }
      .message-time {
        margin-top: 4px;
        font-size: 11px;
        color: #9ca3af;
      }

      /* 移动端优化 */
      @media screen and (width <= 768px) {
        .device-preview {
          grid-template-columns: 1fr;
        }
        .card-header {
          flex-direction: column;
          gap: 8px;
          align-items: flex-start;
        }
        .card-action {
          align-self: flex-end;
        }
        .chart-container,
        .message-list {
          height: 150px;
        }
      }

      @media screen and (width <= 480px) {
        .container {
          padding: 20px;
        }
        .device {
          padding: 15px;
        }
        .device-screen {
          min-height: 500px;
          padding: 12px;
        }
        .chart-container,
        .message-list {
          height: 120px;
        }
      }
      .viewport-info {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 100;
        padding: 8px 12px;
        font-size: 12px;
        color: white;
        background: rgb(0 0 0 / 80%);
        border-radius: 6px;
      }
      .code-section {
        padding: 20px;
        margin: 20px 0;
        overflow-x: auto;
        font-family: Monaco, Menlo, "Ubuntu Mono", monospace;
        font-size: 14px;
        line-height: 1.5;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
      }
      .code-title {
        margin-bottom: 10px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
        font-weight: 600;
        color: #495057;
      }
    </style>
  </head>
  <body>
    <div class="viewport-info" id="viewportInfo">视口: <span id="viewport">计算中...</span></div>

    <div class="container">
      <h1 class="title">🔧 移动端首页布局修复完成</h1>

      <div class="fix-summary">
        <h3>✅ 已修复的首页布局问题</h3>
        <div class="fix-item">添加响应式栅格布局：移动端单列，桌面端双列</div>
        <div class="fix-item">优化卡片间距：移动端减小padding和margin</div>
        <div class="fix-item">调整图表高度：移动端适配不同屏幕尺寸</div>
        <div class="fix-item">改进按钮布局：移动端垂直排列，节省空间</div>
        <div class="fix-item">优化消息列表：移动端滚动和字体大小调整</div>
        <div class="fix-item">统一视觉风格：保持毛玻璃效果和动画</div>
      </div>

      <div class="device-preview">
        <!-- 桌面端预览 -->
        <div class="device">
          <div class="device-screen desktop">
            <div class="device-title">桌面端 (1200px+)</div>
            <div class="home-layout">
              <div class="card">
                <div class="card-header">
                  <div class="card-title">设备统计</div>
                  <button class="card-action">刷新</button>
                </div>
                <div class="card-body">
                  <div class="chart-container">📊 设备统计图表</div>
                </div>
              </div>

              <div class="card">
                <div class="card-header">
                  <div class="card-title">消息中心</div>
                  <button class="card-action">刷新</button>
                </div>
                <div class="card-body">
                  <div class="message-list">
                    <div class="message-item">
                      <div class="message-title">系统通知</div>
                      <div class="message-content">设备A离线，请检查网络连接</div>
                      <div class="message-time">2024-01-15 10:30</div>
                    </div>
                    <div class="message-item">
                      <div class="message-title">告警信息</div>
                      <div class="message-content">路由器B CPU使用率过高</div>
                      <div class="message-time">2024-01-15 09:15</div>
                    </div>
                    <div class="message-item">
                      <div class="message-title">维护提醒</div>
                      <div class="message-content">交换机C需要固件更新</div>
                      <div class="message-time">2024-01-15 08:45</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 移动端预览 -->
        <div class="device">
          <div class="device-screen mobile">
            <div class="device-title">移动端 (768px及以下)</div>
            <div class="home-layout">
              <div class="card">
                <div class="card-header">
                  <div class="card-title">设备统计</div>
                  <button class="card-action">刷新</button>
                </div>
                <div class="card-body">
                  <div class="chart-container">📊 设备统计图表</div>
                </div>
              </div>

              <div class="card">
                <div class="card-header">
                  <div class="card-title">消息中心</div>
                  <button class="card-action">刷新</button>
                </div>
                <div class="card-body">
                  <div class="message-list">
                    <div class="message-item">
                      <div class="message-title">系统通知</div>
                      <div class="message-content">设备A离线</div>
                      <div class="message-time">10:30</div>
                    </div>
                    <div class="message-item">
                      <div class="message-title">告警信息</div>
                      <div class="message-content">CPU使用率过高</div>
                      <div class="message-time">09:15</div>
                    </div>
                    <div class="message-item">
                      <div class="message-title">维护提醒</div>
                      <div class="message-content">需要固件更新</div>
                      <div class="message-time">08:45</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="code-section">
        <div class="code-title">主要修复代码：</div>
        <pre>
// 1. Vue模板响应式栅格
&lt;el-row :gutter="20" class="home-row"&gt;
  &lt;el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="chart-col"&gt;
    &lt;!-- 图表模块 --&gt;
  &lt;/el-col&gt;
  &lt;el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="message-col"&gt;
    &lt;!-- 消息模块 --&gt;
  &lt;/el-col&gt;
&lt;/el-row&gt;

// 2. 移动端样式优化
@media screen and (width <= 768px) {
  .main-content {
    padding: 16px;
  }
  
  .chart-col, .message-col {
    margin-bottom: 16px;
  }
  
  .box-card .el-card__header .clearfix {
    flex-direction: column;
    align-items: flex-start;
  }
}

// 3. 容器高度适配
.chart-container, .message-container {
  @media screen and (width <= 768px) {
    height: 350px;
  }
  
  @media screen and (width <= 480px) {
    height: 300px;
  }
}</pre
        >
      </div>
    </div>

    <script>
      // 显示视口信息
      function updateViewportInfo() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        document.getElementById("viewport").textContent = `${width} × ${height}px`;
      }

      updateViewportInfo();
      window.addEventListener("resize", updateViewportInfo);

      // 按钮交互效果
      document.querySelectorAll(".card-action").forEach(btn => {
        btn.addEventListener("click", function () {
          this.style.transform = "scale(0.95)";
          this.textContent = "刷新中...";

          setTimeout(() => {
            this.style.transform = "";
            this.textContent = "刷新";
          }, 500);
        });
      });

      // 消息项交互效果
      document.querySelectorAll(".message-item").forEach(item => {
        item.addEventListener("click", function () {
          this.style.background = "#e0f2fe";
          setTimeout(() => {
            this.style.background = "#f8fafc";
          }, 300);
        });
      });

      console.log("✅ 移动端首页布局修复演示页面已加载");
      console.log("📱 请调整浏览器窗口大小来测试响应式效果");
    </script>
  </body>
</html>
