<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>📝 页尾布局优化对比</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        min-height: 100vh;
        padding: 40px 20px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      .container {
        max-width: 1000px;
        padding: 40px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgb(0 0 0 / 12%);
      }
      .title {
        margin-bottom: 16px;
        font-size: 32px;
        font-weight: 700;
        color: #2c3e50;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .subtitle {
        margin-bottom: 40px;
        font-size: 16px;
        color: #64748b;
        text-align: center;
      }
      .comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 40px;
      }
      .comparison-item {
        padding: 24px;
        background: #f8fafc;
        border: 2px solid transparent;
        border-radius: 12px;
        transition: all 0.3s ease;
      }
      .comparison-item:hover {
        border-color: #3b82f6;
        transform: translateY(-2px);
      }
      .comparison-title {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
      }
      .before .comparison-title {
        color: #dc2626;
      }
      .after .comparison-title {
        color: #059669;
      }
      .demo-footer {
        overflow: hidden;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
      }

      /* 优化前样式 */
      .footer-old {
        height: 50px;
        padding: 0 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-top: 1px solid #e9ecef;
      }
      .footer-old .footer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
      }
      .footer-old .beian-link {
        font-size: 13px;
        color: #6c757d;
        text-decoration: none;
      }
      .footer-old .copyright {
        font-size: 13px;
        color: #495057;
      }

      /* 优化后样式 */
      .footer-new {
        min-height: 60px;
        padding: 16px 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-top: 1px solid #e9ecef;
      }
      .footer-new .footer-content {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 44px;
      }
      .footer-new .footer-main {
        display: flex;
        flex-direction: column;
        gap: 6px;
        align-items: center;
        text-align: center;
      }
      .footer-new .copyright {
        font-size: 14px;
        font-weight: 500;
        color: #1f2937;
        transition: color 0.3s ease;
      }
      .footer-new .copyright:hover {
        color: #3b82f6;
      }
      .footer-new .beian-link {
        font-size: 12px;
        color: #6b7280;
        text-decoration: none;
        opacity: 0.8;
        transition: all 0.3s ease;
      }
      .footer-new .beian-link::before {
        content: "🏛️ ";
      }
      .footer-new .beian-link:hover {
        color: #3b82f6;
        opacity: 1;
        transform: translateY(-1px);
      }
      .improvements {
        padding: 24px;
        background: #f0f9ff;
        border-left: 4px solid #0ea5e9;
        border-radius: 12px;
      }
      .improvements h3 {
        margin-bottom: 16px;
        font-size: 20px;
        color: #0f172a;
      }
      .improvements ul {
        list-style: none;
      }
      .improvements li {
        position: relative;
        padding: 8px 0 8px 28px;
        color: #334155;
      }
      .improvements li::before {
        position: absolute;
        top: 8px;
        left: 0;
        content: "✅";
      }

      @media (width <= 768px) {
        .comparison {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 class="title">📝 页尾布局优化对比</h1>
      <p class="subtitle">版权声明和备案信息布局优化，更和谐的视觉体验</p>

      <div class="comparison">
        <div class="comparison-item before">
          <h3 class="comparison-title">❌ 优化前 - 左右分布</h3>
          <div class="demo-footer">
            <div class="footer-old">
              <div class="footer-content">
                <div class="footer-left">
                  <a class="beian-link" href="#">🏛️ 粤ICP备2023072379号</a>
                </div>
                <div class="footer-right">
                  <span class="copyright">2023-2025 © 深圳市众通源科技发展有限公司版权所有</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="comparison-item after">
          <h3 class="comparison-title">✅ 优化后 - 居中垂直</h3>
          <div class="demo-footer">
            <div class="footer-new">
              <div class="footer-content">
                <div class="footer-main">
                  <div class="copyright">2023-2025 © 深圳市众通源科技发展有限公司版权所有</div>
                  <div class="beian-info">
                    <a class="beian-link" href="#">粤ICP备2023072379号</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="improvements">
        <h3>🎯 主要改进</h3>
        <ul>
          <li>布局更和谐：从左右分布改为居中垂直布局</li>
          <li>层次更分明：版权信息在上方更突出，备案信息在下方</li>
          <li>字体优化：版权信息使用更大字体，备案信息使用较小字体</li>
          <li>颜色对比：版权信息颜色更深，备案信息颜色较浅</li>
          <li>间距改善：增加页尾高度和内容间距</li>
          <li>交互提升：添加悬停效果，提供更好的用户反馈</li>
        </ul>
      </div>
    </div>
  </body>
</html>
