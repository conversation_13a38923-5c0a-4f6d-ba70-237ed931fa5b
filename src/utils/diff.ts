import { isEqual, isPlainObject } from "lodash";

/**
 * 返回 obj2 相比 obj1 发生变化的部分（只返回有变化的字段，递归对比）
 */
export function getDiff(obj1: any, obj2: any): any {
  if (isEqual(obj1, obj2)) return undefined;
  if (!isPlainObject(obj1) || !isPlainObject(obj2)) return obj2;

  const diff: any = {};
  for (const key of Object.keys(obj2)) {
    if (!isEqual(obj1[key], obj2[key])) {
      if (isPlainObject(obj2[key]) && isPlainObject(obj1[key])) {
        const childDiff = getDiff(obj1[key], obj2[key]);
        if (childDiff !== undefined) diff[key] = childDiff;
      } else {
        diff[key] = obj2[key];
      }
    }
  }
  return Object.keys(diff).length > 0 ? diff : undefined;
}
