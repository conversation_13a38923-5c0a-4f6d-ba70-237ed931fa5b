// 获取设备信息工具
export function getDeviceInfo() {
  // 获取设备名
  let deviceName = navigator.userAgent;
  // 判断设备类型
  let deviceType = "PC";
  const ua = navigator.userAgent.toLowerCase();
  if (/mobile|android|iphone|ipod|phone|blackberry|iemobile|opera mini/.test(ua)) {
    deviceType = "MOBILE";
  } else if (/ipad|tablet|playbook|silk/.test(ua)) {
    deviceType = "TABLET";
  }
  // 获取/生成 uuid
  let uuid = localStorage.getItem("device-uuid");
  if (!uuid) {
    uuid = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    localStorage.setItem("device-uuid", uuid);
  }
  return { deviceName, deviceType, uuid };
}
