import { useGlobalStore } from "@/stores/modules/global";
import { debounce } from "lodash-es";

/**
 * 响应式布局工具函数
 * 根据屏幕宽度自动调整侧边栏折叠状态
 */
export const useResponsive = () => {
  const globalStore = useGlobalStore();

  // 判断当前设备是否为移动设备
  const isMobile = () => {
    return window.innerWidth < 768;
  };

  // 根据屏幕宽度调整侧边栏折叠状态
  const adjustSidebarCollapse = () => {
    if (isMobile()) {
      // 在移动设备上自动折叠侧边栏
      globalStore.setGlobalState("isCollapse", true);
    } else {
      // 在桌面设备上展开侧边栏（除非用户手动折叠）
      // 这里可以根据需求调整，也可以保持用户的选择不变
    }
  };

  // 使用防抖处理窗口大小变化事件
  const handleResize = debounce(() => {
    adjustSidebarCollapse();
  }, 100);

  // 初始化响应式布局
  const initResponsive = () => {
    // 初始化时调整一次
    adjustSidebarCollapse();

    // 监听窗口大小变化
    window.addEventListener("resize", handleResize);
  };

  // 清理事件监听
  const destroyResponsive = () => {
    window.removeEventListener("resize", handleResize);
  };

  return {
    isMobile,
    initResponsive,
    destroyResponsive
  };
};
