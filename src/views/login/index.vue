<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <div class="login-actions">
        <SwitchDark class="dark-switch" />
        <SwitchLanguage class="language-switch" />
      </div>
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left6.png" alt="login" />
      </div>
      <div class="login-form">
        <div class="login-logo">
          <img class="login-icon" src="@/assets/images/logo.png" alt="" />
          <h2 class="logo-text">
            <div ref="chart" class="chart-container"></div>
          </h2>
        </div>
        <LoginForm />
      </div>
    </div>
  </div>
  <div class="beian-wrapper">
    <div class="footer-info">
      <div class="copyright">{{ $t("footer.copyright") }}</div>
      <div class="beian-link-wrapper">
        <a class="beian-link" href="https://beian.miit.gov.cn" target="_blank" :title="$t('footer.beianLink')">
          {{ $t("footer.beianNumber") }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import LoginForm from "./components/LoginForm.vue";
import SwitchDark from "@/components/SwitchDark/index.vue";
import SwitchLanguage from "@/components/SwitchLanguage/index.vue";
import * as echarts from "echarts";
import { onMounted, ref } from "vue";

const chart = ref<HTMLDivElement | null>(null);

onMounted(() => {
  if (chart.value) {
    const myChart = echarts.init(chart.value);
    const option = {
      graphic: {
        elements: [
          {
            type: "text",
            left: "center",
            top: "center",
            style: {
              text: "CloudIot",
              fontSize: 57,
              fontWeight: "bold",
              lineDash: [0, 200],
              lineDashOffset: 0,
              fill: "transparent",
              stroke: "#6DA9E8",
              lineWidth: 1
            },
            keyframeAnimation: {
              duration: 3000,
              loop: true,
              keyframes: [
                {
                  percent: 0.7,
                  style: {
                    fill: "transparent",
                    lineDashOffset: 200,
                    lineDash: [200, 0]
                  }
                },
                {
                  // Stop for a while.
                  percent: 0.9,
                  style: {
                    fill: "transparent"
                  }
                },
                {
                  percent: 0.9,
                  style: {
                    fill: "#387DDE"
                  }
                },
                {
                  percent: 1, // 添加结束帧
                  style: {
                    fill: "#387DDE" // 根据需求设置最终状态
                  }
                }
              ]
            }
          }
        ]
      }
    };
    myChart.setOption(option);
  }
});
</script>

<style scoped lang="scss">
@import "./index";
.chart-container {
  width: 300px;
  height: 120px; // 根据需要调整高度
}

// 添加一些微妙的动画效果
@keyframes footer-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.beian-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100vw;
  padding: 16px 20px;
  background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 95%) 30%, rgb(255 255 255 / 98%) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgb(255 255 255 / 20%);
  transition: all 0.3s ease;
  animation: footer-fade-in 0.8s ease-out 0.5s both;
  &:hover {
    background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 98%) 20%, rgb(255 255 255 / 100%) 100%);
  }
  .footer-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    text-align: center;
  }
  .copyright {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    color: #333333;
    letter-spacing: 0.3px;
    transition: color 0.3s ease;
    &:hover {
      color: #387dde;
    }
  }
  .beian-link-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .beian-link {
    display: inline-flex;
    gap: 4px;
    align-items: center;
    font-size: 12px;
    color: #666666;
    text-decoration: none;
    letter-spacing: 0.2px;
    opacity: 0.8;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    &::before {
      font-size: 12px;
      content: "🏛️";
      opacity: 0.7;
      transition: all 0.3s ease;
    }
    &:hover {
      color: #387dde;
      text-decoration: none;
      filter: drop-shadow(0 2px 4px rgb(56 125 222 / 20%));
      opacity: 1;
      transform: translateY(-1px);
      &::before {
        opacity: 1;
        transform: scale(1.05);
      }
    }
    &:active {
      transform: translateY(0);
    }
  }

  // 响应式设计
  @media screen and (width <= 768px) {
    padding: 12px 16px;
    .beian-link {
      gap: 4px;
      font-size: 12px;
      &::before {
        font-size: 13px;
      }
    }
    .copyright {
      margin-top: 6px;
      font-size: 11px;
    }
  }

  @media screen and (width <= 480px) {
    padding: 10px 12px;
    .beian-link {
      flex-direction: column;
      gap: 2px;
      font-size: 11px;
      &::before {
        font-size: 12px;
      }
    }
    .copyright {
      margin-top: 4px;
      font-size: 10px;
      line-height: 1.4;
    }
  }

  // 深色模式适配
  @media (prefers-color-scheme: dark) {
    background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 90%) 30%, rgb(0 0 0 / 95%) 100%);
    border-top-color: rgb(255 255 255 / 10%);
    &:hover {
      background: linear-gradient(180deg, rgb(0 0 0 / 0%) 0%, rgb(0 0 0 / 95%) 20%, rgb(0 0 0 / 100%) 100%);
    }
    .copyright {
      color: #dddddd;
      &:hover {
        color: #409eff;
      }
    }
    .beian-link {
      color: #cccccc;
      &:hover {
        color: #409eff;
      }
    }
  }
}
</style>
