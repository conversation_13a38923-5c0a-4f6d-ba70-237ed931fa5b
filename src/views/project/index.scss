/* 卡片样式 */
.card {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: space-between; /* 垂直分布内容 */
  width: 110px;
  min-height: 60px;
  padding: 6px 2px;
  margin-bottom: 2px;
  text-align: center;
  background: var(--el-bg-color);
  border-radius: 10px;
  box-shadow: none;
  transition: box-shadow 0.2s;
  &:hover {
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 8%);
  }
}

/* 图片部分的行样式 */
.image-row {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  justify-content: start; /* 左对齐 */
  margin-bottom: 8px; /* 与文字部分留出间距 */
}

/* 图片样式 */
.device-img {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
  object-fit: contain; /* 确保图片保持比例 */
}

/* 标题样式 */
.title {
  margin-bottom: 2px;
  font-size: 13px;
  font-weight: 500;
}

/* 限制文字行数，避免超出 */
.card p {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: var(--el-color-primary);
}

/* 为每张卡片增加统一间距 */
//.table-box {
//  display: flex;
//  flex-wrap: wrap;
//  gap: 20px; /* 添加上下和左右的间距 */
//}

/* 整体容器样式 */
.container {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  min-height: unset;
  padding: 6px 6px 0;
  margin-bottom: 8px;
  background-color: var(--el-bg-color);
  border: 1px solid #eeeeee;
  border-radius: 10px;
}
.card-space {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 10px !important;
  justify-content: flex-start;
}

/* 卡片样式 */
.no-border {
  padding: 0; /* 移除卡片内的间距 */
  background-color: transparent; /* 移除背景色 */
  border: none; /* 移除边框 */
  box-shadow: none; /* 移除阴影 */
}

/* 动画 */
.smooth-collapse-enter-active,
.smooth-collapse-leave-active {
  transition:
    max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.5s ease;
}
.smooth-collapse-enter-from,
.smooth-collapse-leave-to {
  max-height: 0;
  opacity: 0;
}
.smooth-collapse-leave-from,
.smooth-collapse-enter-to {
  max-height: 500px; /* 根据内容调整 */
  opacity: 1;
}

/* 箭头图标容器 */
.arrow-container {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin: 0 auto;
  margin-top: -8px;
  cursor: pointer;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
  transition:
    box-shadow 0.2s,
    background 0.2s;
  &:hover {
    background: var(--el-fill-color-light);
    box-shadow: 0 4px 12px rgb(0 0 0 / 12%);
  }
}

/* SVG箭头图标 */
.arrow-icon {
  width: 12px;
  height: 12px;
  transition: transform 0.3s;
  &.up {
    transform: rotate(180deg);
  }
  &.down {
    transform: rotate(0deg);
  }
}
.el-scrollbar {
  width: 100%;
}
.main-box {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  height: calc(100vh - 120px); /* 设置合适的高度 */
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 6px 20px rgb(0 0 0 / 6%);
  }
  :deep(.tree-filter) {
    flex: 0 0 220px;
    padding: 16px;
    background-color: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
  }
  .table-box {
    display: flex;
    flex: 1;
    flex-direction: column;
    min-width: 0;
    height: 100%;

    /* 标签页头部样式 */
    .tabs-header {
      margin-bottom: 16px;
      .tabs-container {
        /* 优化的标签页样式 */
        :deep(.el-tabs__header) {
          margin-bottom: 0;
          border-bottom: none;
        }
        :deep(.el-tabs__nav-wrap) {
          &::after {
            /* 移除底部线条，使用自定义样式 */
            height: 0;
          }
        }
        :deep(.el-tabs__nav) {
          padding: 2px;
          background-color: var(--el-fill-color-light);
          border: none;
          border-radius: 8px;
        }
        :deep(.el-tabs__item) {
          height: 40px;
          padding: 0 20px;
          font-size: 15px;
          font-weight: 500;
          line-height: 40px;
          color: var(--el-text-color-secondary);
          border: none !important;
          border-radius: 6px;
          transition: all 0.3s ease;
          &.is-active {
            font-weight: 600;
            color: var(--el-color-primary);
            background-color: var(--el-bg-color);
            box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
          }
          &:hover:not(.is-active) {
            color: var(--el-color-primary);
          }
        }

        /* 确保标签页内容区域样式正确 */
        :deep(.el-tabs__content) {
          display: none; /* 隐藏默认的内容区域，因为我们使用自定义的内容区域 */
        }
      }
    }

    /* 标签页内容区域 */
    .tabs-content {
      position: relative;
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;

      /* 拓扑图标签页内容 */
      .topology-tab-content {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        height: 100%;
        overflow: hidden;

        /* 让D3Topology组件充满容器 */
        :deep(.topology-wrapper) {
          display: flex;
          flex: 1;
          flex-direction: column;
          width: 100% !important;
          height: 100% !important;
          overflow: hidden;

          /* 确保悬浮按钮可见 */
          .floating-buttons {
            position: fixed !important; /* 确保始终使用fixed定位 */
            z-index: 9999 !important; /* 使用!important确保优先级 */
          }
        }
      }

      /* 项目目录标签页内容 */
      .project-tab-content {
        position: relative;
        height: 100%;
        overflow: auto;
      }
    }
  }
  :deep(.el-table) {
    overflow: hidden;
    border-radius: 8px;
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
    td {
      padding: 12px 0;
    }
    .el-table__row {
      transition: all 0.3s ease;
      &:hover {
        background-color: var(--el-fill-color-light);
      }
    }
    .el-table__expand-icon {
      transition: all 0.3s ease;
      &:hover {
        transform: scale(1.1);
      }
    }
  }
  :deep(.el-pagination) {
    justify-content: flex-end;
    padding: 0 20px;
    margin-top: 20px;
  }
  :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }
    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }
  :deep(.el-button) {
    transition: all 0.3s ease;
    &:hover {
      box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      transform: translateY(-1px);
    }
  }
  :deep(.el-tag) {
    height: 24px;
    padding: 0 8px;
    line-height: 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
    &:hover {
      transform: translateY(-1px);
    }
  }
  :deep(.el-table__header-wrapper) {
    th {
      font-weight: 600;
      color: var(--el-text-color-primary);
      background-color: var(--el-bg-color-page);
    }
  }
  :deep(.el-table__body-wrapper) {
    td {
      color: var(--el-text-color-regular);
    }
  }
  :deep(.el-empty) {
    padding: 40px 0;
  }
}

// 响应式布局
@media screen and (width <= 768px) {
  .main-box {
    flex-direction: column;
    padding: 10px;
    :deep(.tree-filter) {
      width: 100%;
      margin-bottom: 20px;
    }
  }
}

// 黑暗模式样式
html.dark {
  // 箭头容器黑暗模式样式
  .arrow-container {
    background-color: var(--el-bg-color-overlay); // 使用叠加背景色
    border-color: var(--el-border-color-darker); // 使用更深的边框颜色
    box-shadow: 0 2px 8px rgb(0 0 0 / 20%); // 增强阴影
    &:hover {
      background-color: var(--el-color-info-dark-2); // 悬停时使用更深的背景色
      border-color: var(--el-border-color-dark); // 悬停时边框颜色变化
      box-shadow: 0 4px 12px rgb(0 0 0 / 30%); // 增强阴影
    }
  }

  // 箭头图标黑暗模式样式
  .arrow-icon {
    fill: var(--el-text-color-secondary); // 使用次要文本颜色
    &:hover {
      fill: var(--el-color-primary); // 悬停时使用主色调
    }
  }

  // 容器黑暗模式样式
  .container {
    background-color: var(--el-bg-color); // 使用变量替代固定颜色
    border-color: var(--el-border-color-darker); // 使用更深的边框颜色
    box-shadow: 0 4px 16px rgb(0 0 0 / 15%); // 增强阴影
  }

  // 标签页黑暗模式样式
  .tabs-header {
    .tabs-container {
      :deep(.el-tabs__nav) {
        background-color: var(--el-bg-color-overlay);
      }
      :deep(.el-tabs__item) {
        color: var(--el-text-color-secondary);
        &.is-active {
          color: var(--el-color-primary);
          background-color: var(--el-bg-color);
          box-shadow: 0 2px 8px rgb(0 0 0 / 20%);
        }
        &:hover:not(.is-active) {
          color: var(--el-color-primary-light-3);
        }
      }
    }
  }
}
.tree-filter {
  padding: 20px;
  margin-right: 20px;
  background-color: var(--el-bg-color-page); // 使用变量替代固定颜色
  border-right: 1px solid var(--el-border-color-light); // 使用变量替代固定颜色
  border-radius: 4px;
}
.tree-filter .el-dropdown {
  margin-top: 10px;
}
.tree-filter .el-dropdown-item {
  color: var(--el-text-color-regular); // 使用变量替代固定颜色
  &:hover {
    color: var(--el-color-primary); // 使用变量替代固定颜色
    background-color: var(--el-color-primary-light-9); // 使用变量替代固定颜色
  }
}

// 黑暗模式下的树形过滤器样式
html.dark .tree-filter {
  background-color: var(--el-bg-color-overlay); // 使用叠加背景色
  border-right: 1px solid var(--el-border-color-darker); // 使用更深的边框颜色
  box-shadow: 0 2px 12px rgb(0 0 0 / 20%); // 增强阴影
  .el-dropdown-item {
    &:hover {
      color: var(--el-color-white); // 黑暗模式下的悬停文本颜色
      background-color: var(--el-color-primary-dark-2); // 黑暗模式下的悬停背景色
    }
  }
}

/* 收起时隐藏统计区，只保留按钮 */
.project-tab-content {
  .container {
    overflow: hidden;
    transition:
      max-height 0.3s,
      opacity 0.3s;
    &.collapsed {
      max-height: 0 !important;
      padding: 0;
      margin-bottom: 0;
      border: none;
      opacity: 0;
    }
  }
}
.collapse-btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 28px;
  padding: 0;
  margin: 0;
}
.collapse-btn {
  display: flex;
  gap: 4px;
  align-items: center;
  height: 24px;
  padding: 0 12px;
  font-size: 13px;
  color: var(--el-text-color-regular);
  cursor: pointer;
  user-select: none;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  outline: none;
  box-shadow: 0 1px 4px rgb(0 0 0 / 4%);
  transition:
    background 0.2s,
    box-shadow 0.2s,
    color 0.2s,
    border-color 0.2s;
  &:hover {
    color: var(--el-color-primary);
    background: var(--el-fill-color-light);
    border-color: var(--el-color-primary);
  }
}
.collapse-btn-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 0 0 4px;
}
.collapse-divider {
  width: 100%;
  height: 1px;
  margin-bottom: 1px;
  background: var(--el-border-color-lighter);
}
.collapse-link-btn {
  display: flex;
  gap: 4px;
  align-items: center;
  height: 18px;
  padding: 0 8px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  cursor: pointer;
  user-select: none;
  background: transparent;
  border: none;
  border-radius: 3px;
  outline: none;
  transition:
    color 0.2s,
    background 0.2s;
  &:hover {
    color: var(--el-color-primary);
    background: var(--el-fill-color-light);
  }
}
.card-content-row {
  display: flex;
  gap: 6px;
  align-items: baseline;
  justify-content: center;
}
.card-number {
  font-size: 22px;
  font-weight: bold;
  line-height: 1;
  color: var(--el-color-primary);
}
.card-label {
  font-size: 13px;
  font-weight: 500;
  line-height: 1;
  color: var(--el-text-color-regular);
}
