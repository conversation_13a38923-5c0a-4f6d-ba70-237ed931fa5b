<template>
  <div class="main-box">
    <TreeFilter
      ref="treeFilterRef"
      label="rnetName"
      :title="t('common.remoteNetwork')"
      :request-api="getRnetList"
      :default-value="initParam.id"
      @change="changeTreeFilter"
    >
      <template #dropdown>
        <el-dropdown-item :icon="CirclePlus" @click="addDialogVisible = true">
          {{ $t("common.add") }}
        </el-dropdown-item>
        <el-dropdown-item :icon="Delete" @click="deleteGroup">
          {{ $t("common.delete") }}
        </el-dropdown-item>
      </template>
    </TreeFilter>
    <!-- 右侧主内容 -->
    <div class="main-content card">
      <el-card class="box-card">
        <template #header>
          <div class="clearfix">
            <span style="font-weight: bold">{{ $t("common.remoteNetwork") }}</span>
            <el-button type="primary" class="action-button refresh-button" :icon="Refresh" @click="refreshData">
              {{ t("common.refresh") }}
            </el-button>
            <el-button type="primary" class="action-button add-button" style="float: right" @click="addRnetDevice">
              <el-icon style="margin-right: 5px">
                <Plus />
              </el-icon>
              {{ $t("common.add") }}
            </el-button>
          </div>
        </template>
        <el-container style="height: 650px">
          <el-aside style="width: 70%">
            <div class="card content-box">
              <!-- 使用 D3NetworkGraph 组件 -->
              <D3NetworkGraph
                ref="networkRef"
                :key="forceRerender"
                :data="networkData"
                :width="chartWidth"
                :height="chartHeight"
                @node-click="handleNodeClick"
                @refresh="handleNodeRefresh"
              />
            </div>
          </el-aside>
          <el-main style="display: flex; flex-direction: column; padding: 0 10px; overflow: hidden">
            <div class="device-list-header">
              <div class="title-section">
                <span class="font-bold">{{ $t("common.remoteNetworkDevices") }}</span>
              </div>
            </div>
            <div class="device-list-container">
              <el-card class="box-card" v-for="device in devices" :key="device.deviceId">
                <div class="card-content">
                  <strong>
                    {{ device.deviceId }}
                    {{
                      device.deviceName && device.deviceName.trim() && device.deviceName !== device.deviceId
                        ? "(" + device.deviceName + ")"
                        : ""
                    }}
                  </strong>
                  <el-dropdown trigger="click">
                    <el-icon size="20" style="margin-right: 0; cursor: pointer; transform: rotate(90deg) translateY(-8px)">
                      <More />
                    </el-icon>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="configRnetDevice(device)">
                          <el-icon>
                            <Tools />
                          </el-icon>
                          {{ $t("device.configuration") }}
                        </el-dropdown-item>
                        <el-dropdown-item
                          @click="deleteRnetDevice({ rnetId: initParam.id, peer: [{ deviceId: device.deviceId }] })"
                        >
                          <el-icon>
                            <Delete />
                          </el-icon>
                          {{ $t("common.delete") }}
                        </el-dropdown-item>
                        <slot name="dropdown" />
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
                <el-row>
                  <el-col>
                    <span>
                      <el-text :class="device.status == 0 ? 'online-text' : 'offline-text'">
                        • {{ device.status == 0 ? $t("common.onLine") : $t("common.offLine") }}
                      </el-text>
                    </span>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col>
                    <el-text>
                      {{
                        isChinese
                          ? deviceTypes.find(type => type.configCode.toUpperCase() === device.deviceType)?.configDesc
                          : device.deviceType.toUpperCase()
                      }}
                    </el-text>
                  </el-col>
                </el-row>
                <el-row>
                  <el-text>
                    <el-text style="font-weight: bold">{{ $t("common.networkType") }}:</el-text>
                    {{
                      device?.data?.system?.rNet?.stun === 0
                        ? "CODE"
                        : device?.data?.system?.rNet?.stun === 1
                          ? "SYMMETRIC"
                          : "---"
                    }}
                  </el-text>
                </el-row>
                <el-row>
                  <el-text>
                    <el-text style="font-weight: bold">{{ $t("common.mappingIP") }}:</el-text>
                    {{ device?.data?.system?.rNet?.wan ? device?.data?.system?.rNet?.wan : "---" }} ->
                    {{ device?.data?.system?.rNet?.map ? device?.data?.system?.rNet?.map : "---" }}
                  </el-text>
                </el-row>
                <el-row>
                  <el-text>
                    <el-text style="font-weight: bold">{{ $t("common.allowedAccessIPSegment") }}:</el-text>
                    {{ device?.data?.system?.rNet?.allowedIPs ? device?.data?.system?.rNet?.allowedIPs : "---" }}
                  </el-text>
                </el-row>
              </el-card>
            </div>
          </el-main>
        </el-container>
      </el-card>
    </div>
    <el-dialog
      v-model="addDialogVisible"
      :title="t('common.add')"
      width="30%"
      class="custom-dialog"
      @closed="rnetForm.rnetName = ''"
    >
      <div class="dialog-container">
        <div class="dialog-body">
          <el-form ref="rnetFormRef" :model="rnetForm" :rules="rnetRules" label-suffix=" :">
            <el-form-item :label="t('device.name')" prop="rnetName">
              <el-input v-model="rnetForm.rnetName" :placeholder="t('device.namePlaceholder')" />
            </el-form-item>
          </el-form>
        </div>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button type="primary" @click="addRnet">
            {{ $t("common.confirm") }}
          </el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-model="addRnetDeviceVisible"
      :title="t('device.addDevice')"
      class="custom-dialog"
      width="70%"
      @open="handleRnetDeviceOpen"
    >
      <div class="dialog-container">
        <div class="dialog-header" style="display: flex; align-items: center; justify-content: flex-end; margin-bottom: 10px">
          <el-checkbox v-model="selectAll" @change="handleSelectAll">{{ $t("common.selectAll") }}</el-checkbox>
        </div>
        <div class="dialog-body" style="display: flex; flex-wrap: wrap; height: 400px; overflow-y: auto">
          <el-card v-for="item in allDevices" :key="item.deviceId" class="device-card" :class="{ 'selected-card': item.checked }">
            <div class="device-info" style="height: 90%">
              <el-row>
                <el-col :span="12">
                  <img :src="getDeviceImg(item.deviceType)" class="device-img" />
                </el-col>
                <el-col :span="12">
                  <div class="device-type">
                    {{ isChinese ? deviceTypes.find(type => type.configCode === item.deviceType)?.configDesc : item.deviceType }}
                  </div>
                  <div class="device-status">
                    <el-tag v-if="item.status == 0" type="success">{{ $t("common.onLine") }}</el-tag>
                  </div>
                </el-col>
              </el-row>
              <div class="device-name">
                <el-text>
                  <strong>{{ item.deviceName }}</strong>
                </el-text>
                <div class="device-mac">
                  <el-text>{{ item.deviceId }}</el-text>
                  <el-checkbox v-model="item.checked" @change="() => handleChecked(item)" style="margin-left: 8px"></el-checkbox>
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <div class="dialog-footer">
          <el-button @click="closeRnetDevices()">
            {{ $t("common.cancel") }}
          </el-button>
          <el-button type="primary" @click="saveRnetDevices()">
            {{ $t("common.confirm") }}
          </el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="configRnetDeviceVisible">
      <el-form ref="configForm" :model="form" :rules="rules" label-width="220px">
        <el-form-item :label="t('common.segmentGenerationMode')" prop="custom">
          <el-switch
            v-model="form.data.system.rNet.custom"
            :active-value="0"
            :active-text="t('common.generateAutomatically')"
            :inactive-value="1"
            :inactive-text="t('common.customization')"
          />
        </el-form-item>
        <el-form-item :label="t('common.allowedAccessIPSegment')" prop="allowedIPs">
          <div v-for="(ip, index) in form.data.system.rNet.allowedIPs" :key="index" style="display: flex; align-items: center">
            <el-form-item :prop="'data.system.rNet.allowedIPs.' + index" :rules="ipRules">
              <el-input
                v-model="form.data.system.rNet.allowedIPs[index]"
                :placeholder="t('device.ipPlaceholder')"
                style="flex: 1; margin-right: 10px"
              />
            </el-form-item>
            <el-button type="danger" circle @click="removeIp(index)" v-if="form.data.system.rNet.custom === 1">
              <el-icon><Minus /></el-icon>
            </el-button>
          </div>
          <el-button type="primary" circle @click="addIp" v-if="form.data.system.rNet.custom === 1">
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">
            {{ $t("common.confirm") }}
          </el-button>
          <el-button @click="cancelRnetDeviceConfigFn">
            {{ $t("common.cancel") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, provide, reactive, ref, watch, computed } from "vue";
import TreeFilter from "@/views/rnet/components/TreeFilter.vue";
import i18n from "@/languages/index";
import { ElMessage, ElMessageBox } from "element-plus";
import { CirclePlus, Delete, Plus, Minus, More, Tools, Refresh } from "@element-plus/icons-vue";
import D3NetworkGraph from "@/components/D3NetworkGraph/index.vue";
import { createRnet, deleteRnet, getDeviceImg, getDeviceList, getRnetList, rnetForm, form } from "@/api/modules/rnet";
import { getRnetDevice, pollRnetConfigs } from "@/api/modules/rnetD3";
import { getDeviceType } from "@/api/modules/project";
import { useGlobalStore } from "@/stores/modules/global";
import {
  addRnetDevice,
  addRnetDeviceVisible,
  configRnetDeviceVisible,
  getRnetDeviceConfigJwe,
  submitRnetDeviceConfig,
  handleChecked as originalHandleChecked,
  addRnetDevices,
  removeRnetDevice,
  selectedDevices
} from "@/api/interface/rnet";

const t = i18n.global.t;
const globalStore = useGlobalStore();
const isChinese = globalStore.language === "zh";

let addDialogVisible = ref(false);
const initParam = reactive({ id: "" });
const deviceTypes = ref<any[]>([]);
const devices = ref<any[]>([]);

// D3网络图相关状态
const networkData = ref({ nodes: [], links: [] });
const chartWidth = ref(800);
const chartHeight = ref(580);
const networkRef = ref(null);

// 用于强制重新渲染组件
const forceRerender = ref(1); // 初始值设为1，确保组件首次渲染

const rnetDeviceConfig = ref();

const saveRnetDevices = async () => {
  // const currDeviceIds = devices.value.map(device => device.deviceId);
  const res = await addRnetDevices({ rnetId: initParam.id }, t);
  console.log("res:", JSON.stringify(res));
  if (res.code === "200") {
    ElMessage.success({
      message: t("common.operationSuccess")
    });
    // 重置全选状态
    selectAll.value = false;
    closeRnetDevices();
    // 重新获取设备列表
    await loadRnetTopologyData(initParam.id);
  }
};

const deleteRnetDevice = async (params: { rnetId: string; peer: [{ deviceId: string }] }) => {
  // 显示确认对话框
  ElMessageBox.confirm(t("common.deleteConfirm"), t("common.confirm"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
    type: "warning"
  }).then(async () => {
    // 用户点击确定，执行删除操作
    const res = await removeRnetDevice(params);
    if (res.code === "200") {
      ElMessage.success(t("common.operationSuccess"));
      // 重新获取设备列表
      await loadRnetTopologyData(initParam.id);
      // 强制重新渲染网络图
      forceRerender.value++;
    }
  });
};

const allDevices = ref<any[]>([]);
const countySymbol = ref<number | null>(null); // 使用 Ref 类型
const selectAll = ref(false); // 全选状态

// 处理全选/取消全选
const handleSelectAll = (val: boolean) => {
  // 更新所有设备的选中状态
  allDevices.value.forEach(device => {
    device.checked = val;
    // 如果选中，添加到selectedDevices；如果取消选中，从selectedDevices中移除
    if (val) {
      // 避免重复添加
      if (!selectedDevices.value.some(item => item.deviceId === device.deviceId)) {
        selectedDevices.value.push(device);
      }
    } else {
      selectedDevices.value = selectedDevices.value.filter(item => item.deviceId !== device.deviceId);
    }
  });
  console.log("全选状态:", val, "已选设备数量:", selectedDevices.value.length);
};

// 处理单个设备选中状态变化
const handleChecked = (device: any) => {
  // 调用原始的handleChecked函数处理选中逻辑
  originalHandleChecked(device);

  // 检查是否所有设备都被选中，更新全选状态
  const allChecked = allDevices.value.every(item => item.checked);
  if (selectAll.value !== allChecked) {
    selectAll.value = allChecked;
  }
};

// 关闭设备对话框
const closeRnetDevices = () => {
  // 重置全选状态
  selectAll.value = false;
  // 直接设置对话框不可见并清空选中设备
  addRnetDeviceVisible.value = false;
  selectedDevices.value = [];
};

const handleRnetDeviceOpen = async () => {
  // 重置全选状态
  selectAll.value = false;

  const res = await getDeviceList();
  if (res.code === "200") {
    allDevices.value = Array.isArray(res.data) ? res.data : [];
  }

  // 使用 filter 方法移除已经存在于 devices.value 中的设备，并移除离线的设备，以及移除不支持 rNet 或 rnetId 不为空的设备
  allDevices.value = allDevices.value.filter(device => {
    // 移除已经存在于 devices.value 中的设备
    if (devices.value.some(d => d.deviceId === device.deviceId)) {
      return false;
    }
    // 移除离线的设备（假设 status 为 "1" 表示离线）
    if (device.status === 1) {
      return false;
    }
    // 移除不支持 rNet 的设备
    if (!device.supports.includes("rNet")) {
      return false;
    }
    device.checked = false;
    // 移除 rnetId 不为空的设备
    return !device.rnetId;
  });

  // 如果分组中有设备，则获取第一个设备，并获取其 deviceId 的最后一位
  if (devices.value.length > 0) {
    const firstDevice = devices.value[0];
    let deviceId = firstDevice.deviceId;
    // 如果 deviceId 是 15 位，则取最后一位
    if (deviceId.length === 15) {
      countySymbol.value = parseInt(deviceId.slice(-1));
    } else {
      countySymbol.value = 0;
    }

    // 过滤同一个服务器下的设备
    allDevices.value = allDevices.value.filter(device => {
      // 如果 countySymbol 不为 0，则过滤掉不匹配的设备
      if (countySymbol.value !== null) {
        if (countySymbol.value !== 0) {
          // 如果 countySymbol 是 1，则过滤掉不匹配的设备
          if (!device.deviceId.endsWith(countySymbol.value.toString())) {
            return false;
          }
        } else {
          // 如果 countySymbol 是 0，则过滤掉 deviceId 长度为 15 且不匹配的设备
          if (device.deviceId.length === 15 && !device.deviceId.endsWith("0")) {
            return false;
          }
        }
      }
      return true;
    });
  }
};

const addIp = () => {
  form.data.system.rNet.allowedIPs.push("");
};

const removeIp = (index: number) => {
  if (form.data.system.rNet.allowedIPs.length > 1) {
    form.data.system.rNet.allowedIPs.splice(index, 1);
  } else {
    ElMessage.warning(t("common.allowedIPsMinTip") || t("common.operationFailed"));
  }
};

// 树形筛选切换
const changeTreeFilter = async (val: string) => {
  console.log(`树形筛选切换为: ${val}`);

  // 更新参数
  initParam.id = val;

  // 清空网络图数据
  networkData.value = { nodes: [], links: [] };

  // 清空设备列表，避免重复
  devices.value = [];

  // 强制重新渲染网络图 - 重新创建组件
  forceRerender.value++;

  // 加载新的拓扑图数据
  if (val) {
    // 使用 await 确保数据加载完成
    await loadRnetTopologyData(val);
  }
};
const treeFilterRef = ref();

// 新增远程网络
const addRnet = async () => {
  if (!rnetFormRef.value) return;
  await rnetFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const res = await createRnet(rnetForm);
      if (res.code === "200") {
        ElMessage.success(t("common.success"));
        treeFilterRef.value.refreshData();
      } else {
        ElMessage.error(t("common.failed"));
      }
      addDialogVisible.value = false;
    }
  });
};

// 删除远程网络
// 刷新数据
const refreshData = async () => {
  console.log(`刷新数据: ${initParam.id}`);

  // 清空设备列表，避免重复
  devices.value = [];

  // 加载数据
  await loadRnetTopologyData(initParam.id);

  ElMessage.success(t("common.refreshSuccess"));
};

// 更新网络图数据
const updateNetworkData = currentDevices => {
  if (!currentDevices || currentDevices.length === 0) {
    networkData.value = { nodes: [], links: [] };
    return;
  }

  // 只显示在线设备在网络图中（status === 0）
  const onlineDevices = currentDevices.filter(device => device.status === 0);

  // 全量重建 nodes - 只包含在线设备
  const nodes = onlineDevices.map(device => ({
    id: device.deviceId,
    deviceId: device.deviceId,
    name: device.deviceName || "",
    symbol: getDeviceImg(device.deviceType),
    status: device.status,
    deviceType: device.deviceType,
    data: device.data || {},
    rate: device.rate || "0 B/s"
  }));

  // 全量重建 links - 只包含在线设备之间的连接
  const links = [];
  const onlineDeviceIds = new Set(onlineDevices.map(d => d.deviceId));
  const linkSet = new Set();
  onlineDevices.forEach(device => {
    if (device.data?.system?.rNet?.peer) {
      device.data.system.rNet.peer.forEach(peer => {
        if (onlineDeviceIds.has(peer.deviceId)) {
          const [sourceId, targetId] = [device.deviceId, peer.deviceId].sort();
          const linkId = `${sourceId}-${targetId}`;
          if (!linkSet.has(linkId)) {
            linkSet.add(linkId);
            links.push({
              source: device.deviceId,
              target: peer.deviceId,
              status: peer.status || "SUCCESS",
              medium: peer.medium || "CABLE",
              direction: "normal",
              rxByte: Number(peer.rxByte) || 0,
              txByte: Number(peer.txByte) || 0
            });
          }
        }
      });
    }
  });

  // 赋值新对象，确保响应式
  networkData.value = {
    nodes,
    links
  };
};

// 加载远程网络拓扑图数据
const loadRnetTopologyData = async rnetId => {
  if (!rnetId) return;

  try {
    devices.value = [];
    const res = await getRnetDevice(rnetId, devices.value);
    if (res && Array.isArray(res.data)) {
      updateNetworkData(devices.value);
      console.log("devices after reload", devices.value);
    }
  } catch (error) {
    console.error("Failed to load network data:", error);
  }
};

// 删除远程网络
const deleteGroup = async () => {
  if (!initParam.id) {
    ElMessage.error(t("common.selectRemoteNetworkTip"));
    return;
  }
  await ElMessageBox.confirm(t("common.deleteRemoteNetworkTip"), t("common.confirm"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
    type: "warning"
  })
    .then(async () => {
      const res = await deleteRnet(initParam);
      if (res.code === "200") {
        ElMessage.success(t("common.deleteRemoteNetworkSuccess"));
        treeFilterRef.value.refreshData();
      } else {
        ElMessage.error(t("common.deleteRemoteNetworkFail"));
      }
    })
    .catch(() => {
      console.log("Cancelled");
    });
};

// 配置节点配置信息
const configRnetDevice = async (device: any) => {
  configRnetDeviceVisible.value = true;
  rnetDeviceConfig.value = await getRnetDeviceConfigJwe({ deviceId: device.deviceId });
  if (rnetDeviceConfig.value && rnetDeviceConfig.value.code === "200") {
    const rNetData = rnetDeviceConfig.value;
    // 逐层赋值
    form.deviceId = rNetData.deviceId;
    form.data.system.rNet.custom = rNetData.data.system.rNet.custom;
    form.data.system.rNet.allowedIPs =
      (!rNetData.data.system.rNet.allowedIPs?.length ? null : rNetData.data.system.rNet.allowedIPs) ||
      (() => {
        const device = devices.value.find(device => device.deviceId === rNetData.deviceId);
        return device?.data?.system?.rNet?.allowedIPs || [""];
      })();
    form.data.system.rNet.rnetId = rNetData.data.system.rNet.rnetId || "";
  }
};

// 配置异地组网设备信息
const submitRnetDeviceConfigFn = async (params: { deviceId: string; config: any }) => {
  const res = await submitRnetDeviceConfig(params);
  if (res.code === "200") {
    ElMessage.success(t("common.success"));
    configRnetDeviceVisible.value = false;
    // 配置成功后，强制全量刷新
    await loadRnetTopologyData(initParam.id);
  } else {
    ElMessage.error(t("common.failed"));
  }
};

// 取消异地组网设备信息
const cancelRnetDeviceConfigFn = () => {
  configRnetDeviceVisible.value = false;
  form.deviceId = "";
  form.data.system.rNet.custom = 0;
  form.data.system.rNet.allowedIPs = [""];
  form.data.system.rNet.rnetId = "";
};

// 监听 initParam.id 变化，更新设备数据及图表
watch(
  () => initParam.id,
  async (newValue, oldValue) => {
    if (newValue !== oldValue && newValue !== "") {
      await loadRnetTopologyData(newValue);
    }
  }
);

// 处理节点点击事件
const handleNodeClick = node => {
  console.log("Node clicked:", node);
  // 如果是根节点（互联网），不显示设备信息
  if (node.data.isRoot) return;

  // 显示设备信息
  const deviceInfo = devices.value.find(device => device.deviceId === node.data.deviceId);
  if (deviceInfo) {
    // 更新节点数据，确保弹窗显示最新信息
    Object.assign(node.data, deviceInfo);
    console.log("Device info:", deviceInfo);
  }
};

// 处理节点刷新事件
const handleNodeRefresh = async node => {
  console.log("Refreshing node:", node);

  // 如果是根节点，不执行刷新
  if (node.isRoot) return;

  // 重新获取设备数据
  if (initParam.id && node.deviceId) {
    // 显示加载提示
    ElMessage.info({
      message: t("common.refreshing"),
      duration: 1000
    });

    // 重新加载数据，但不显示全局加载状态
    await loadRnetTopologyData(initParam.id);

    // 更新节点数据
    const updatedDevice = devices.value.find(device => device.deviceId === node.deviceId);
    if (updatedDevice) {
      // 更新节点数据，确保弹窗显示最新信息
      Object.assign(node, updatedDevice);

      // 显示成功消息
      ElMessage.success({
        message: t("common.refreshSuccess"),
        duration: 1000
      });
    }
  }
};

// 监听主题变化
watch(
  () => globalStore.isDark,
  () => {
    console.log("Theme changed, refreshing network graph...");
    // 当主题变化时，刷新网络图
    // 重新加载数据
    if (initParam.id) {
      loadRnetTopologyData(initParam.id);
    }
  }
);

// 轮询更新：每 5 秒更新数据
let pollInterval: ReturnType<typeof setInterval> | null = null;

// 启动轮询
const startPolling = () => {
  // 先停止现有轮询，防止重复启动
  stopPolling();

  console.log("Starting polling for data...");

  try {
    pollInterval = setInterval(async () => {
      try {
        // 定时刷新拓扑图数据，但不刷新设备列表
        if (initParam.id) {
          // 只更新网络图数据，不重新加载设备列表
          // 获取当前设备列表的副本
          const currentDevices = [...devices.value];
          // 更新设备数据
          await pollRnetConfigs(currentDevices);
          // 更新网络图数据
          updateNetworkData(currentDevices);
        }
      } catch (error) {
        console.error("Error in polling interval:", error);
      }
    }, 5000);
    console.log("Polling started successfully");
  } catch (error) {
    console.error("Failed to start polling:", error);
  }
};

// 停止轮询
const stopPolling = () => {
  try {
    if (pollInterval) {
      console.log("Stopping polling...");
      clearInterval(pollInterval);
      pollInterval = null;
      console.log("Polling stopped successfully");
    }
  } catch (error) {
    console.error("Error stopping polling:", error);
    // 强制清除
    pollInterval = null;
  }
};

onMounted(async () => {
  console.log("Component mounted, initializing topology...");

  // 等待DOM完全渲染
  await nextTick();

  // 初始化图表尺寸
  updateChartSize();

  // 再次更新图表尺寸，确保它能正确计算
  setTimeout(() => {
    updateChartSize();
    // 强制重新渲染一次
    forceRerender.value++;
  }, 300);

  // 监听窗口大小变化
  window.addEventListener("resize", resizeHandler);

  // 获取设备类型数据
  const response = await getDeviceType();
  if (response && response.data) {
    deviceTypes.value = Array.isArray(response.data) ? response.data : [];
    provide("deviceTypes", deviceTypes);
  }

  // 如果有初始参数，加载拓扑图数据
  if (initParam.id) {
    await loadRnetTopologyData(initParam.id);
  }

  // 启动轮询，定时更新数据
  startPolling();
});

// 存储resize事件处理函数的引用
const resizeHandler = () => {
  updateChartSize();
  // 强制重新渲染一次
  forceRerender.value++;
};

onBeforeUnmount(() => {
  // 停止轮询
  stopPolling();

  // 移除事件监听器
  window.removeEventListener("resize", resizeHandler);
});

const ipRules = computed(() => [
  {
    required: true,
    message: t("device.ipRequired"),
    trigger: "blur"
  },
  {
    pattern: /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/,
    message: t("device.invalidIpFormat"),
    trigger: "blur"
  },
  {
    validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback();
      } else {
        const [ipPart, maskPart] = value.split("/");
        const mask = parseInt(maskPart);
        if (mask < 0 || mask > 32) {
          callback(new Error(t("device.invalidMask")));
        } else {
          const ipParts = ipPart.split(".");
          const invalidPart = ipParts.some(part => {
            const num = parseInt(part);
            return num < 0 || num > 255;
          });
          if (invalidPart) {
            callback(new Error(t("device.invalidIpRange")));
          } else {
            callback();
          }
        }
      }
    },
    trigger: "blur"
  }
]);

const rules = computed(() => ({
  "data.system.rNet.custom": [
    {
      required: true,
      message: t("device.customRequired"),
      trigger: "change"
    }
  ],
  "data.system.rNet.allowedIPs": [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (form.data.system.rNet.custom === 1) {
          if (!value || value.length === 0) {
            callback(new Error(t("device.ipRequired")));
          } else if (value.some((ip: string) => !ip)) {
            callback(new Error(t("device.ipRequired")));
          } else {
            // 检查格式
            const ipFormat = /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/;
            if (value.some((ip: string) => !ipFormat.test(ip))) {
              callback(new Error(t("device.invalidIpFormat")));
            } else {
              callback();
            }
          }
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
}));

const rnetFormRef = ref();
const rnetRules = computed(() => ({
  rnetName: [
    {
      required: true,
      message: t("device.nameRequired"),
      trigger: "blur"
    },
    {
      max: 32,
      message: t("device.nameMaxLength"),
      trigger: "blur"
    }
  ]
}));

// 更新图表尺寸
const updateChartSize = () => {
  const container = document.querySelector(".content-box");
  if (container) {
    chartWidth.value = container.clientWidth || 800;
    // 确保图表高度足够大，留出一些边距
    chartHeight.value = (container.clientHeight || 580) - 20;
    console.log(`Chart size updated: ${chartWidth.value}x${chartHeight.value}`);
  }
};

// 监听 devices 变化，实时刷新网络图
watch(
  devices,
  newVal => {
    updateNetworkData(newVal);
  },
  { deep: true, immediate: true }
);

const configForm = ref();

const onSubmit = () => {
  configForm.value.validate((valid: boolean) => {
    if (valid) {
      submitRnetDeviceConfigFn({ deviceId: form.deviceId, config: form.data });
    }
  });
};
</script>

<style scoped lang="scss">
@import "./index";
.main-content {
  position: relative;
  max-height: 100%;
  overflow-y: auto;
}
.el-container {
  align-items: stretch;
}
.el-aside {
  display: flex;
  flex-direction: column;
}
.el-main {
  display: flex;
  flex-direction: column;
}
.card.content-box {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.device-card {
  flex-shrink: 0;
  width: 220px;
  margin: 5px;
  transition: all 0.3s;
}
.selected-card {
  border: 2px solid #409eff;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
.dialog-body {
  gap: 10px;
  justify-content: flex-start;
}
</style>
