<template>
  <div class="profile-container">
    <!-- 背景装饰元素 -->
    <div class="decoration-element decoration-top-right"></div>
    <div class="decoration-element decoration-bottom-left"></div>
    <div class="decoration-element decoration-circle-1"></div>
    <div class="decoration-element decoration-circle-2"></div>

    <div class="profile-content">
      <!-- 个人信息卡片 -->
      <div class="profile-card main-card">
        <!-- 头像区域 -->
        <div class="profile-header">
          <div class="avatar-container">
            <div class="avatar-wrapper">
              <el-avatar :size="120" :src="headDefault" :fit="fit"></el-avatar>
              <div class="avatar-status"></div>
            </div>
            <div class="user-info">
              <h2 class="user-name" v-if="userinfo?.nickName">{{ userinfo.nickName }}</h2>
              <h2 class="user-name empty" v-else>{{ $t("common.notSet") }}</h2>
              <div class="user-role" v-if="userinfo?.role">
                <el-tag size="small" type="success" effect="light" round>
                  <el-icon><UserFilled /></el-icon>
                  {{ userinfo.role || "普通用户" }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 基本信息区域 -->
        <div class="profile-section">
          <h3 class="section-title">{{ $t("user.basicInfo") || "基本信息" }}</h3>

          <!-- 昵称区域 -->
          <div class="info-item">
            <div class="info-label">{{ $t("user.nickname") }}</div>
            <div class="info-content">
              <el-input
                v-if="editNick"
                v-model="userinfo.nickName"
                clearable
                class="input-field"
                :maxlength="50"
                :show-word-limit="true"
                :placeholder="$t('user.inputNickname') || '请输入昵称'"
              >
                <template #prefix>
                  <el-icon class="input-icon"><User /></el-icon>
                </template>
              </el-input>
              <div v-else class="info-value" :class="{ empty: !userinfo?.nickName }">
                <el-icon><User /></el-icon>
                <span v-if="userinfo?.nickName">{{ userinfo.nickName }}</span>
                <span v-else>{{ $t("common.notSet") }}</span>
              </div>
            </div>
            <div class="info-action">
              <el-button v-if="!editNick" type="primary" size="small" class="action-btn" @click="editNick = true">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button v-else type="success" size="small" class="action-btn" @click="updateNick()">
                <el-icon><Check /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 邮箱区域 -->
          <div class="info-item">
            <div class="info-label">{{ $t("user.email") }}</div>
            <div class="info-content">
              <div class="info-value" :class="{ empty: !userinfo?.email }">
                <el-icon><Message /></el-icon>
                <span v-if="userinfo?.email">{{ maskEmail(userinfo.email) }}</span>
                <span v-else>{{ $t("common.notSet") }}</span>
              </div>
            </div>
            <div class="info-action">
              <el-button
                v-if="!userinfo?.email || userinfo?.email.trim().length === 0"
                type="primary"
                size="small"
                class="action-btn"
                @click="openEmailDrawer(t('common.binding'))"
              >
                <el-icon><Link /></el-icon>
              </el-button>
              <el-button
                v-else
                type="primary"
                size="small"
                class="action-btn"
                @click="openEmailDrawer(t('common.modifyBinding'))"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 手机区域 -->
          <div class="info-item">
            <div class="info-label">{{ $t("user.phone") }}</div>
            <div class="info-content">
              <div class="info-value" :class="{ empty: !userinfo?.phone }">
                <el-icon><Phone /></el-icon>
                <span v-if="userinfo?.phone || userinfo?.phone?.trim().length > 0">{{ maskPhone(userinfo.phone) }}</span>
                <span v-else>{{ $t("common.notSet") }}</span>
              </div>
            </div>
            <div class="info-action">
              <el-button
                v-if="!userinfo?.phone || userinfo?.phone.trim().length === 0"
                type="primary"
                size="small"
                class="action-btn"
                @click="openPhoneDrawer(t('common.binding'))"
              >
                <el-icon><Link /></el-icon>
              </el-button>
              <el-button
                v-else
                type="primary"
                size="small"
                class="action-btn"
                @click="openPhoneDrawer(t('common.modifyBinding'))"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷链接卡片 -->
      <div class="profile-card links-card">
        <h3 class="section-title">{{ $t("common.quickLinks") || "快捷链接" }}</h3>
        <div class="quick-links">
          <div class="link-item" @click="openPasswordDrawer($t('common.modify'))" ref="changePasswordRef">
            <div class="link-icon">
              <el-icon><Lock /></el-icon>
            </div>
            <div class="link-text">{{ $t("header.changePassword") || "修改密码" }}</div>
          </div>
          <div class="link-item" @click="feedbackDialog()">
            <div class="link-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="link-text">{{ $t("common.feedback") || "意见反馈" }}</div>
          </div>
          <div class="link-item" @click="fetchAgreement(1)">
            <div class="link-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="link-text">{{ $t("common.userAgreement") || "用户协议" }}</div>
          </div>
          <div class="link-item" @click="fetchAgreement(2)">
            <div class="link-icon">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="link-text">{{ $t("common.privacyPolicy") || "隐私政策" }}</div>
          </div>
        </div>
      </div>

      <!-- 退出登录按钮 -->
      <div class="logout-container">
        <el-button type="danger" @click="logout" class="logout-button">
          <el-icon><SwitchButton /></el-icon>
          {{ $t("header.logout") || "退出登录" }}
        </el-button>
      </div>
    </div>

    <!-- 对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="80%" class="custom-dialog">
      <div v-html="dialogContent" style="padding: 20px"></div>
    </el-dialog>

    <!-- 反馈对话框 -->
    <el-dialog v-model="feedbackDialogVisible" :title="t('common.feedback') || '意见反馈'" width="40%" class="feedback-dialog">
      <el-form ref="feedbackForm" :model="feedback">
        <el-form-item :label="$t('common.feedback') || '反馈内容'" label-width="100px" prop="feedback">
          <el-input
            v-model="feedback.feedback"
            type="textarea"
            :rows="4"
            :maxlength="1000"
            :show-word-limit="true"
            :placeholder="t('common.feedbackTip') || '请输入您的反馈内容'"
            resize="vertical"
          />
        </el-form-item>
        <el-form-item :label="$t('common.contact') || '联系方式'" label-width="100px" prop="contact">
          <el-input v-model="feedback.contact" :placeholder="t('common.contactTip') || '请留下您的联系方式（选填）'" />
        </el-form-item>
        <el-form-item class="form-foot">
          <el-button type="primary" @click="submitFeedback">
            <el-icon><Check /></el-icon>
            {{ t("common.confirm") || "确认" }}
          </el-button>
          <el-button @click="feedbackDialogVisible = false">
            <el-icon><Close /></el-icon>
            {{ t("common.cancel") || "取消" }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-tour v-model="tourOpen">
      <el-tour-step :target="changePasswordRef?.$el" :title="t('header.changePassword')"> </el-tour-step>
    </el-tour>
    <PhoneDrawer ref="drawerPhoneRef" />
    <EmailDrawer ref="drawerEmailRef" />
    <PasswordDrawer ref="drawerPasswordRef" />
  </div>
</template>
<script setup lang="ts" name="mine">
import { ref, onMounted, provide, reactive, onBeforeUnmount } from "vue";
import EmailDrawer from "@/views/mine/components/EmailDrawer.vue";
import { getUserInfo } from "@/api/modules/login";
import { getNewestAgreement, saveFeedback, updateEmail, updateNickName, updatePassword, updatePhone } from "@/api/modules/mine";
import { ButtonInstance, ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { logoutApi } from "@/api/modules/login";
import { useI18n } from "vue-i18n";
import PhoneDrawer from "@/views/mine/components/PhoneDrawer.vue";
import PasswordDrawer from "@/views/mine/components/PasswordDrawer.vue";
import headDefault from "@/assets/images/logo-circle.png";
import { useUserStore } from "@/stores/modules/user";
import { useRouter } from "vue-router";
import { LOGIN_URL } from "@/config";
import {
  SwitchButton,
  Edit,
  Check,
  Message,
  Phone,
  Link,
  Lock,
  ChatDotRound,
  Document,
  InfoFilled,
  Close,
  User,
  UserFilled
} from "@element-plus/icons-vue";
import bus from "@/utils/bus";

const router = useRouter();
const userStore = useUserStore();

const { t, locale } = useI18n();
let tourOpen = ref(false);
const changePasswordRef = ref<ButtonInstance>();

// 存储设备型号的数据
const deviceTypes = ref([]);

const userinfo = ref();
const fit = ref("cover");

interface Feedback {
  feedback: string;
  contact: string;
}

const feedback = reactive<Feedback>({
  feedback: "",
  contact: ""
});

const feedbackForm = ref<FormInstance>();

const dialogVisible = ref(false);
const feedbackDialogVisible = ref(false);
const dialogTitle = ref("");
const dialogContent = ref("");

// 退出登录
const logout = () => {
  ElMessageBox.confirm(t("header.confirmLogout"), t("header.warmRemind"), {
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
    type: "warning"
  }).then(async () => {
    // 1.执行退出登录接口
    await logoutApi();

    // 2.清除 Token
    userStore.setToken("");

    // 3.重定向到登陆页
    await router.replace(LOGIN_URL);
    ElMessage.success(t("header.logoutSuccess"));
  });
};

const submitFeedback = async () => {
  if (!feedback.feedback || feedback.feedback.length > 1000) {
    ElMessage.error(t("common.feedbackLengthTip"));
    return;
  }
  try {
    const response = await saveFeedback({
      feedback: feedback.feedback,
      contact: feedback.contact
    });
    if (response.code === "200") {
      ElMessage.success(t("common.feedbackSuccess"));
      feedbackDialogVisible.value = false;
      feedbackForm.value.resetFields();
    } else {
      ElMessage.error(t("common.feedbackFail"));
    }
  } catch (error) {
    console.error("请求失败:", error);
    ElMessage.error(t("common.feedbackFail"));
  }
};

interface AgreementData {
  title: string;
  content: string;
}

const fetchAgreement = async (type: number) => {
  try {
    console.log("开始获取协议...");
    // 获取当前语言
    const language = locale.value === "en" ? "en_US" : "zh_CN";
    const response = await getNewestAgreement({ type }, { "Accept-Language": language });
    console.log("API响应:", response);
    if (response.code === "200") {
      const agreementData = response.data as AgreementData;
      dialogTitle.value = agreementData.title;
      dialogContent.value = agreementData.content;
      dialogVisible.value = true; // 确保设置为true
      console.log("对话框应显示");
    } else {
      ElMessage.error(response.msg);
    }
  } catch (error) {
    console.error("请求失败:", error);
    ElMessage.error(t("common.fetchFailed"));
  }
};

const feedbackDialog = async () => {
  feedbackDialogVisible.value = true;
};

let editNick = ref(false);

// 打开 drawer(新增、查看、编辑)
const drawerPhoneRef = ref<InstanceType<typeof PhoneDrawer> | null>(null);

const drawerEmailRef = ref<InstanceType<typeof EmailDrawer> | null>(null);

const drawerPasswordRef = ref<InstanceType<typeof PasswordDrawer> | null>(null);

// 在 setup 中调用 provide，提供初始值
provide("deviceTypes", deviceTypes);

const updateNick = async () => {
  if (!userinfo.value.nickName || userinfo.value.nickName.length > 50) {
    ElMessage.error(t("user.nicknameRuleTip"));
    return;
  }
  console.log("updateNick----", userinfo.value.nickName);
  editNick.value = false;
  // 更新昵称
  const res = await updateNickName({ nickName: userinfo.value.nickName });
  if (res.code === "200") {
    // 更新昵称成功
    ElMessage.success({ message: t("common.modifySuccess") });
    // 刷新用户信息
    const response = await getUserInfo();
    if (response && response.data) {
      userinfo.value = response.data;
      provide("userinfo", userinfo.value);
      console.log("存入userinfo:", userinfo.value);
    }
  } else {
    console.log("更新昵称失败");
    editNick.value = true;
    ElMessage.error({ message: res.msg });
    return;
  }
};

const maskEmail = (email?: string): string => {
  if (!email) return "";
  const atIndex = email.lastIndexOf("@");
  const name = email.substring(0, atIndex);
  const domain = email.substring(atIndex);

  const maskedName =
    name.length > 1
      ? name.charAt(0) +
        "*".repeat(name.length - 1 - (name.length > 3 ? 1 : 0)) +
        (name.length > 3 ? name.charAt(name.length - 1) : "")
      : "*";

  return maskedName + domain;
};

/**
 * 对手机号进行脱敏处理
 * @param phone - 手机号
 * @returns 脱敏后的手机号或原值（如果手机号为空或尾号不足4位）
 */
const maskPhone = (phone?: string): string => {
  if (!phone) return "";

  // 提取手机号中的数字部分
  const processedPhone = phone.replace(/\D/g, "");

  // 如果手机号长度小于 7 位，直接返回原手机号
  if (processedPhone.length < 7) return phone;

  // 获取数字部分前3位和后4位
  const prefix = processedPhone.substr(0, 3);
  const suffix = processedPhone.substr(processedPhone.length - 4);
  const maskedPart = "*".repeat(processedPhone.length - 3 - 4);

  // 将数字部分替换为前3位 + **** + 后4位
  const maskedNumber = `${prefix}${maskedPart}${suffix}`;

  // 替换原手机号中的数字部分为脱敏后的数字部分
  return phone.replace(/\d+/g, maskedNumber);
};

const openEmailDrawer = (title: string) => {
  const params = {
    title,
    isView: false,
    row: userinfo.value,
    parentRow: {}, // Add empty parentRow to match the interface
    api: updateEmail,
    onSuccess: async () => {
      try {
        // 邮箱更新成功后刷新用户信息
        const response = await getUserInfo();

        if (response && response.code === "200" && response.data) {
          // 使用新的对象替换整个 userinfo.value
          userinfo.value = { ...response.data };

          // 重新提供给子组件
          provide("userinfo", userinfo.value);
        }
      } catch (error) {
        console.error("刷新用户信息时出错:", error);
      }
    }
  };
  drawerEmailRef.value?.acceptParams(params);
};

const openPhoneDrawer = (title: string) => {
  const params = {
    title,
    isView: false,
    row: userinfo.value,
    parentRow: {}, // Add empty parentRow to match the interface
    api: updatePhone,
    onSuccess: async () => {
      try {
        // 手机号更新成功后刷新用户信息
        const response = await getUserInfo();

        if (response && response.code === "200" && response.data) {
          // 使用新的对象替换整个 userinfo.value
          userinfo.value = { ...response.data };

          // 重新提供给子组件
          provide("userinfo", userinfo.value);
        }
      } catch (error) {
        console.error("刷新用户信息时出错:", error);
      }
    }
  };
  drawerPhoneRef.value?.acceptParams(params);
};

const openPasswordDrawer = (title: string) => {
  const params = {
    title,
    isView: false,
    row: userinfo.value,
    parentRow: {}, // Add empty parentRow to match the interface
    api: updatePassword
  };
  drawerPasswordRef.value?.acceptParams(params);
};

const handleOpenPasswordDrawer = () => {
  drawerPasswordRef.value?.acceptParams({
    title: t("header.changePassword"),
    isView: false,
    row: userinfo.value,
    parentRow: {},
    api: updatePassword
  });
};

// 在组件挂载时加载数据
onMounted(async () => {
  const response = await getUserInfo();
  if (response && response.data) {
    userinfo.value = response.data;
    provide("userinfo", userinfo.value);
    console.log("存入userinfo:", userinfo.value);
  }
  bus.on("open-password-drawer", handleOpenPasswordDrawer);

  // 检查路由参数，自动弹出 PasswordDrawer
  if (router.currentRoute.value.query.openPassword) {
    handleOpenPasswordDrawer();
    // 只清除浏览器地址栏参数，不走 router.replace，避免多开 tab
    window.history.replaceState(null, "", "/mine");
  }
});

onBeforeUnmount(() => {
  bus.off("open-password-drawer", handleOpenPasswordDrawer);
});

const openTour = () => {
  tourOpen.value = true;
};

defineExpose({
  openTour
});
</script>
<style scoped lang="scss">
@import "./index";
</style>
