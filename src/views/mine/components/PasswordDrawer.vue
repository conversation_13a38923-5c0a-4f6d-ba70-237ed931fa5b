<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    :title="`${drawerProps.title} ${t('user.password')}`"
    size="680px"
    @close="onDrawerClose"
    class="glass-dialog"
  >
    <el-steps :active="currentStep" simple>
      <el-step :title="t('user.verifyIdentity')" icon="el-icon-edit"></el-step>
      <el-step :title="t('user.setPassword')" icon="el-icon-upload"></el-step>
    </el-steps>
    <div v-if="currentStep === 0">
      <el-form
        ref="validFormRef"
        :hide-required-asterisk="drawerProps.isView"
        :model="formData"
        :rules="formRules"
        label-suffix=" :"
        label-width="160px"
      >
        <el-form-item :label="t('user.emailOrPhone')" prop="emailOrPhone">
          <el-input v-model="formData.emailOrPhone" :placeholder="t('user.emailOrPhoneTip')"></el-input>
        </el-form-item>
        <el-form-item :label="t('user.code')" prop="code">
          <verification-code
            v-model="formData.code"
            :value="formData.emailOrPhone"
            type="auto"
            :step="currentStep"
            @update:model-value="updateCode"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="currentStep === 1">
      <el-form ref="passwordFormRef" :model="formData" :rules="passwordRules" label-suffix=" :" label-width="100px">
        <el-form-item :label="t('user.password')" prop="password">
          <el-input
            v-model="formData.password"
            :placeholder="t('device.passwordPlaceholder')"
            clearable
            type="password"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" :loading="loading" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" name="PasswordDrawer" setup>
import { ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import type { Project } from "@/api/interface/project";
import { updatePassword, validationVerificationCode } from "@/api/modules/mine";
import { logoutApi } from "@/api/modules/login";
import { useRouter } from "vue-router";
import { LOGIN_URL } from "@/config";
import { useUserStore } from "@/stores/modules/user";
import VerificationCode from "@/components/VerificationCode/index.vue";
import { getPasswordRules, getEmailOrPhoneRules, getCodeRules } from "@/utils/formValidation";

const { t } = useI18n();
const router = useRouter();
const userStore = useUserStore();
const drawerVisible = ref(false);
const currentStep = ref(0);
const loading = ref(false);
const originalEmail = ref("");
const originalPhone = ref("");

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<any>;
  parentRow: Partial<Project.ResDeviceList>;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {
    email: "",
    code: "",
    phone: ""
  },
  parentRow: {}
});

interface PasswordFormData {
  emailOrPhone: string;
  code: string;
  password: string;
}

let formData = ref<Partial<PasswordFormData>>({
  emailOrPhone: "",
  code: "",
  password: ""
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = JSON.parse(JSON.stringify(params));
  originalEmail.value = params.row.email || ""; // 记录原始邮箱值
  originalPhone.value = params.row.phone || "";
  drawerProps.value.row.email = "";
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const validFormRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();

// 更新验证码方法
const updateCode = (value: string) => {
  formData.value.code = value;
};

// 动态生成表单验证规则
const formRules = computed(() => {
  if (currentStep.value === 0) {
    // 邮箱或手机号和验证码规则
    return {
      ...getEmailOrPhoneRules(),
      ...getCodeRules()
    };
  } else {
    // 密码验证规则
    return getPasswordRules();
  }
});

const handleSubmit = async () => {
  // 表单验证
  if (currentStep.value === 0 && !validFormRef.value) return;
  if (currentStep.value === 1 && !passwordFormRef.value) return;

  const formRef = currentStep.value === 0 ? validFormRef.value : passwordFormRef.value;
  const valid = await formRef.validate().catch(() => false);
  if (!valid) return;

  loading.value = true;
  try {
    if (currentStep.value === 0) {
      // 第一步：验证身份
      let params: any;
      if (formData.value.emailOrPhone.includes("@")) {
        params = {
          email: formData.value.emailOrPhone,
          code: formData.value.code
        };
      } else {
        params = {
          phone: formData.value.emailOrPhone,
          code: formData.value.code
        };
      }

      const response = await validationVerificationCode(params);

      if (!response || response.code !== "200") {
        ElMessage.error({ message: response?.msg || t("common.requestFailed") });
        return;
      }

      // 验证成功，进入第二步
      currentStep.value = 1;

      // 重置表单数据，保留邮箱或手机号
      const emailOrPhone = formData.value.emailOrPhone;
      formData.value = {
        emailOrPhone,
        code: "",
        password: ""
      };
    } else {
      // 第二步：设置新密码
      // 创建一个符合API类型的对象
      const updatePasswordParams: {
        phone: string;
        code: string;
        userId: string;
        password: string;
      } = {
        phone: formData.value.emailOrPhone.includes("@") ? "" : formData.value.emailOrPhone,
        code: formData.value.code,
        userId: userStore.userInfo.userId,
        password: formData.value.password
      };

      // 如果是邮箱，则不传入email属性
      const response = await updatePassword(updatePasswordParams);

      if (!response || response.code !== "200") {
        ElMessage.error({ message: response?.msg || t("common.requestFailed") });
        return;
      }

      // 密码修改成功
      ElMessage({
        message: t("device.setNewPasswordSuccess"),
        type: "success",
        duration: 3000,
        onClose: async () => {
          drawerVisible.value = false; // 自动关闭弹窗

          // 密码修改成功后，执行退出登录操作
          try {
            // 1.执行退出登录接口
            await logoutApi();

            // 2.清除 Token
            userStore.setToken("");

            // 3.重定向到登录页
            await router.replace(LOGIN_URL);
            ElMessage.success(t("header.logoutSuccess"));
          } catch (error) {
            console.error("退出登录失败:", error);
          }
        }
      });
    }
  } catch (error) {
    console.error("提交表单出错:", error);
    ElMessage.error(t("common.requestFailed"));
  } finally {
    loading.value = false;
  }
};

// 监听关闭事件
const onDrawerClose = () => {
  drawerProps.value.row = {};
  currentStep.value = 0;
  loading.value = false;

  // 重置表单数据
  formData.value = {
    emailOrPhone: "",
    code: "",
    password: ""
  };
};

const passwordRules = getPasswordRules();

defineExpose({
  acceptParams
});
</script>
<style lang="scss" scoped>
.glass-dialog {
  :deep(.el-drawer__header) {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  :deep(.el-steps) {
    margin-bottom: 30px;
  }
  :deep(.el-form) {
    max-width: 500px;
    margin: 0 auto;
  }
  :deep(.el-form-item__content) {
    width: 100%;
  }
}
</style>
