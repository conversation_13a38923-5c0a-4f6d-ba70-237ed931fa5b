/* 响应式样式 */

/* 大屏幕 (>=1200px) */
@media screen and (min-width: 1200px) {
  // 大屏幕下的样式保持默认
}

/* 中等屏幕 (>=992px && <1200px) */
@media screen and (min-width: 992px) and (max-width: 1199px) {
  // 中等屏幕下的样式调整
}

/* 平板 (>=768px && <992px) */
@media screen and (min-width: 768px) and (max-width: 991px) {
  // 平板设备样式调整
  .el-container {
    .el-aside {
      .aside-box {
        width: 180px !important;
      }
    }
  }
}

/* 移动设备 (<768px) */
@media screen and (max-width: 767px) {
  // 移动设备样式调整

  // 移除最小宽度限制
  #app {
    min-width: auto !important;
  }

  // 调整布局容器
  .el-container {
    // 侧边栏在移动设备上默认折叠
    .el-aside {
      .aside-box {
        width: 64px !important;
      }
    }

    // 调整头部样式
    .el-header {
      padding: 0 10px !important;

      // 调整头部组件间距
      .header-lf, .header-ri {
        .header-icon {
          margin-right: 10px !important;
        }
      }

      // 隐藏部分头部元素
      .header-ri {
        .el-breadcrumb {
          display: none !important;
        }
      }
    }

    // 调整主内容区域
    .el-main {
      padding: 10px !important;
    }
  }

  // 调整标签页样式
  .tags-view {
    padding: 0 10px !important;
  }

  // 调整表格和表单在移动设备上的显示
  .el-table {
    width: 100% !important;
    overflow-x: auto !important;
  }

  .el-form {
    .el-form-item {
      margin-bottom: 15px !important;
    }
  }

  // 调整弹窗在移动设备上的宽度
  .el-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  // 调整下拉菜单在移动设备上的宽度
  .el-dropdown-menu {
    min-width: 120px !important;
  }
}

/* 小型移动设备 (<576px) */
@media screen and (max-width: 575px) {
  // 小型移动设备样式调整

  // 进一步调整头部样式
  .el-container {
    .el-header {
      .header-lf {
        .tool-bar-ri {
          display: none !important;
        }
      }

      // 隐藏更多头部元素
      .header-ri {
        .el-dropdown {
          margin-left: 8px !important;
        }

        // 只保留必要的图标
        .header-icon:not(.el-dropdown) {
          display: none !important;
        }
      }
    }
  }

  // 调整表单项在小屏幕上的布局
  .el-form {
    .el-form-item {
      .el-form-item__label {
        display: block !important;
        text-align: left !important;
        margin-bottom: 5px !important;
      }
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
