/* Drawer 专用响应式样式 */
@import "./mixins/responsive.scss";

/* ===== Drawer 内容响应式优化 ===== */

/* Drawer 表单响应式 */
.el-drawer {
  .el-form {
    @include respond-to(md) {
      .el-form-item {
        margin-bottom: var(--spacing-md) !important;
        
        .el-form-item__label {
          display: block !important;
          text-align: left !important;
          margin-bottom: var(--spacing-xs) !important;
          font-size: var(--font-size-sm) !important;
          line-height: 1.4 !important;
          width: 100% !important;
        }
        
        .el-form-item__content {
          margin-left: 0 !important;
          width: 100% !important;
          
          .el-input,
          .el-select,
          .el-textarea,
          .el-date-picker,
          .el-cascader {
            width: 100% !important;
          }
          
          .el-input__wrapper {
            font-size: var(--font-size-sm) !important;
            padding: 8px 12px !important;
          }
          
          .el-textarea__inner {
            font-size: var(--font-size-sm) !important;
            padding: 8px 12px !important;
          }
        }
      }
      
      /* 表单按钮组响应式 */
      .el-form-item__content {
        .el-button-group,
        .form-buttons {
          display: flex !important;
          flex-direction: column !important;
          gap: var(--spacing-sm) !important;
          
          .el-button {
            width: 100% !important;
            margin: 0 !important;
            padding: 10px 16px !important;
            font-size: var(--font-size-sm) !important;
          }
        }
      }
    }
    
    @include respond-to(sm) {
      .el-form-item {
        margin-bottom: 12px !important;
        
        .el-form-item__label {
          font-size: var(--font-size-xs) !important;
          margin-bottom: 4px !important;
        }
        
        .el-form-item__content {
          .el-input__wrapper,
          .el-textarea__inner {
            font-size: var(--font-size-xs) !important;
            padding: 6px 8px !important;
          }
          
          .el-button {
            padding: 8px 12px !important;
            font-size: var(--font-size-xs) !important;
          }
        }
      }
    }
  }
  
  /* Drawer 内的上传组件响应式 */
  .upload-img,
  .upload-imgs {
    @include respond-to(md) {
      width: 100% !important;
      
      .el-upload {
        width: 100% !important;
        
        .el-upload-dragger {
          width: 100% !important;
          height: 120px !important;
        }
      }
    }
    
    @include respond-to(sm) {
      .el-upload {
        .el-upload-dragger {
          height: 100px !important;
        }
      }
    }
  }
  
  /* Drawer 内的选择器响应式 */
  .el-select,
  .el-cascader,
  .el-date-picker {
    @include respond-to(md) {
      width: 100% !important;
      
      .el-input__wrapper {
        width: 100% !important;
      }
    }
  }
  
  /* Drawer 内的单选框/复选框响应式 */
  .el-radio-group,
  .el-checkbox-group {
    @include respond-to(md) {
      display: flex !important;
      flex-direction: column !important;
      gap: var(--spacing-sm) !important;
      
      .el-radio,
      .el-checkbox {
        margin-right: 0 !important;
        margin-bottom: var(--spacing-xs) !important;
        
        .el-radio__label,
        .el-checkbox__label {
          font-size: var(--font-size-sm) !important;
        }
      }
    }
    
    @include respond-to(sm) {
      .el-radio,
      .el-checkbox {
        .el-radio__label,
        .el-checkbox__label {
          font-size: var(--font-size-xs) !important;
        }
      }
    }
  }
  
  /* Drawer 内的标签页响应式 */
  .el-tabs {
    @include respond-to(md) {
      .el-tabs__header {
        margin-bottom: 8px !important;
        
        .el-tabs__nav-wrap {
          overflow-x: auto !important;
          -webkit-overflow-scrolling: touch !important;
        }
      }
      
      .el-tabs__item {
        padding: 0 12px !important;
        font-size: var(--font-size-sm) !important;
        height: 36px !important;
        line-height: 36px !important;
        white-space: nowrap !important;
      }
      
      .el-tabs__content {
        padding: var(--spacing-md) 0 !important;
      }
    }
    
    @include respond-to(sm) {
      .el-tabs__item {
        padding: 0 8px !important;
        font-size: var(--font-size-xs) !important;
        height: 32px !important;
        line-height: 32px !important;
      }
      
      .el-tabs__content {
        padding: var(--spacing-sm) 0 !important;
      }
    }
  }
  
  /* Drawer 内的表格响应式 */
  .el-table {
    @include respond-to(md) {
      font-size: var(--font-size-xs) !important;
      
      .el-table__header th,
      .el-table__body td {
        padding: 6px 4px !important;
        font-size: var(--font-size-xs) !important;
      }
      
      /* 在 Drawer 中隐藏更多列 */
      .el-table__column:nth-child(n+3) {
        display: none !important;
      }
    }
    
    @include respond-to(sm) {
      .el-table__header th,
      .el-table__body td {
        padding: 4px 2px !important;
        font-size: 11px !important;
      }
      
      /* 在小屏幕 Drawer 中只显示前两列 */
      .el-table__column:nth-child(n+2) {
        display: none !important;
      }
    }
  }
  
  /* Drawer 内的步骤条响应式 */
  .el-steps {
    @include respond-to(md) {
      .el-step {
        .el-step__title {
          font-size: var(--font-size-sm) !important;
        }
        
        .el-step__description {
          font-size: var(--font-size-xs) !important;
        }
      }
    }
    
    @include respond-to(sm) {
      .el-step {
        .el-step__title {
          font-size: var(--font-size-xs) !important;
        }
        
        .el-step__description {
          display: none !important;
        }
      }
    }
  }
  
  /* Drawer 底部按钮区域响应式 */
  .drawer-footer {
    @include respond-to(md) {
      padding: var(--spacing-md) !important;
      border-top: 1px solid var(--el-border-color-lighter) !important;
      background: var(--el-bg-color) !important;
      
      .el-button {
        width: 100% !important;
        margin: 0 0 var(--spacing-sm) 0 !important;
        
        &:last-child {
          margin-bottom: 0 !important;
        }
      }
    }
    
    @include respond-to(sm) {
      padding: var(--spacing-sm) !important;
      
      .el-button {
        padding: 8px 16px !important;
        font-size: var(--font-size-sm) !important;
      }
    }
  }
}

/* ===== 特定 Drawer 组件优化 ===== */

/* 用户信息 Drawer */
.user-drawer {
  @include respond-to(md) {
    .el-form-item {
      &.avatar-item,
      &.photo-item {
        .el-form-item__content {
          display: flex !important;
          justify-content: center !important;
        }
      }
    }
  }
}

/* 设备配置 Drawer */
.device-config-drawer {
  @include respond-to(md) {
    .config-tabs {
      .el-tabs__content {
        max-height: 60vh !important;
        overflow-y: auto !important;
      }
    }
  }
}

/* 项目配置 Drawer */
.project-drawer {
  @include respond-to(md) {
    .project-form {
      .el-form-item {
        &.description-item {
          .el-textarea {
            .el-textarea__inner {
              min-height: 80px !important;
            }
          }
        }
      }
    }
  }
}
