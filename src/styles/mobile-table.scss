/* 移动端表格专用优化样式 */
@import "./mixins/responsive.scss";

/* ===== 移动端表格卡片化布局 ===== */

/* 在移动设备上将表格转换为卡片布局 */
@include respond-to(sm) {
  .mobile-table-cards {
    .el-table {
      .el-table__header-wrapper {
        display: none !important; /* 隐藏表头 */
      }
      
      .el-table__body-wrapper {
        .el-table__body {
          .el-table__row {
            display: block !important;
            background: var(--el-bg-color) !important;
            border: 1px solid var(--el-border-color-lighter) !important;
            border-radius: 8px !important;
            margin-bottom: 12px !important;
            padding: 12px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            
            &:hover {
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
            }
            
            .el-table__cell {
              display: flex !important;
              justify-content: space-between !important;
              align-items: center !important;
              border: none !important;
              padding: 6px 0 !important;
              min-height: 32px !important;
              
              &:not(:last-child) {
                border-bottom: 1px solid var(--el-border-color-extra-light) !important;
              }
              
              /* 添加标签 */
              &::before {
                content: attr(data-label) !important;
                font-weight: 600 !important;
                color: var(--el-text-color-secondary) !important;
                font-size: var(--font-size-xs) !important;
                min-width: 80px !important;
                text-align: left !important;
              }
              
              .cell {
                text-align: right !important;
                font-size: var(--font-size-sm) !important;
                color: var(--el-text-color-primary) !important;
                flex: 1 !important;
                margin-left: 8px !important;
              }
            }
          }
        }
      }
    }
  }
}

/* ===== 移动端表格滚动优化 ===== */

@include respond-to(md) {
  .mobile-scroll-table {
    .el-table {
      border-radius: 8px !important;
      overflow: hidden !important;
      
      .el-table__inner-wrapper {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        
        /* 滚动条样式优化 */
        &::-webkit-scrollbar {
          height: 4px !important;
        }
        
        &::-webkit-scrollbar-track {
          background: var(--el-fill-color-extra-light) !important;
        }
        
        &::-webkit-scrollbar-thumb {
          background: var(--el-color-primary-light-5) !important;
          border-radius: 2px !important;
        }
      }
      
      .el-table__header {
        th {
          background: var(--el-fill-color-light) !important;
          font-weight: 600 !important;
          color: var(--el-text-color-primary) !important;
          white-space: nowrap !important;
          
          &:first-child {
            position: sticky !important;
            left: 0 !important;
            z-index: 3 !important;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
          }
          
          &:last-child {
            position: sticky !important;
            right: 0 !important;
            z-index: 3 !important;
            box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
          }
        }
      }
      
      .el-table__body {
        tr {
          &:nth-child(even) {
            background: var(--el-fill-color-extra-light) !important;
          }
          
          td {
            white-space: nowrap !important;
            
            &:first-child {
              position: sticky !important;
              left: 0 !important;
              z-index: 2 !important;
              background: inherit !important;
              box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
            }
            
            &:last-child {
              position: sticky !important;
              right: 0 !important;
              z-index: 2 !important;
              background: inherit !important;
              box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
            }
          }
        }
      }
    }
  }
}

/* ===== 移动端表格操作按钮优化 ===== */

@include respond-to(md) {
  .mobile-table-actions {
    .el-table {
      .table-operation {
        display: flex !important;
        flex-direction: column !important;
        gap: 4px !important;
        
        .el-button {
          width: 100% !important;
          margin: 0 !important;
          padding: 4px 8px !important;
          font-size: var(--font-size-xs) !important;
          height: 24px !important;
          
          &:not(.el-button--primary) {
            display: none !important; /* 隐藏次要按钮 */
          }
        }
        
        .el-dropdown {
          width: 100% !important;
          
          .el-button {
            width: 100% !important;
            justify-content: center !important;
          }
        }
      }
    }
  }
}

@include respond-to(sm) {
  .mobile-table-actions {
    .el-table {
      .table-operation {
        .el-button {
          font-size: 11px !important;
          height: 20px !important;
          padding: 2px 6px !important;
        }
      }
    }
  }
}

/* ===== 移动端表格状态指示器优化 ===== */

@include respond-to(md) {
  .mobile-table-status {
    .el-table {
      .status-indicator {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        
        .el-tag {
          font-size: var(--font-size-xs) !important;
          padding: 2px 6px !important;
          height: 20px !important;
          line-height: 16px !important;
        }
        
        .status-dot {
          width: 8px !important;
          height: 8px !important;
          border-radius: 50% !important;
          margin-right: 4px !important;
          
          &.online {
            background: var(--el-color-success) !important;
          }
          
          &.offline {
            background: var(--el-color-danger) !important;
          }
          
          &.warning {
            background: var(--el-color-warning) !important;
          }
        }
      }
    }
  }
}

/* ===== 移动端表格搜索优化 ===== */

@include respond-to(md) {
  .mobile-table-search {
    .table-search {
      .el-form {
        .el-row {
          .el-col {
            margin-bottom: var(--spacing-sm) !important;
          }
        }
        
        .el-form-item {
          margin-bottom: var(--spacing-sm) !important;
          
          .el-form-item__content {
            .el-input,
            .el-select,
            .el-date-picker {
              width: 100% !important;
            }
          }
        }
        
        .search-buttons {
          display: flex !important;
          gap: var(--spacing-sm) !important;
          
          .el-button {
            flex: 1 !important;
            font-size: var(--font-size-sm) !important;
          }
        }
      }
    }
  }
}

/* ===== 移动端表格分页优化 ===== */

@include respond-to(md) {
  .mobile-table-pagination {
    .el-pagination {
      justify-content: center !important;
      flex-wrap: wrap !important;
      padding: var(--spacing-md) 0 !important;
      
      .el-pagination__total {
        order: 3 !important;
        width: 100% !important;
        text-align: center !important;
        margin: var(--spacing-sm) 0 0 0 !important;
        font-size: var(--font-size-xs) !important;
      }
      
      .el-pagination__sizes {
        order: 1 !important;
        margin: 0 var(--spacing-sm) 0 0 !important;
      }
      
      .el-pager {
        order: 2 !important;
        
        .number {
          min-width: 32px !important;
          height: 32px !important;
          line-height: 32px !important;
          font-size: var(--font-size-sm) !important;
        }
      }
      
      .el-pagination__prev,
      .el-pagination__next {
        width: 32px !important;
        height: 32px !important;
        font-size: var(--font-size-sm) !important;
      }
      
      .el-pagination__jump {
        display: none !important;
      }
    }
  }
}

@include respond-to(sm) {
  .mobile-table-pagination {
    .el-pagination {
      .el-pagination__sizes {
        display: none !important;
      }
      
      .el-pager {
        .number {
          min-width: 28px !important;
          height: 28px !important;
          line-height: 28px !important;
          font-size: var(--font-size-xs) !important;
        }
      }
      
      .el-pagination__prev,
      .el-pagination__next {
        width: 28px !important;
        height: 28px !important;
        font-size: var(--font-size-xs) !important;
      }
    }
  }
}
