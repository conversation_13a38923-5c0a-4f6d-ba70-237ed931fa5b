/* Element Plus 组件响应式优化 */
@import "./mixins/responsive";

/* ===== 全局Element组件响应式样式 ===== */

/* 按钮组件响应式 */
.el-button {
  @include respond-to(md) {
    padding: 8px 12px;
    font-size: var(--font-size-sm);
  }

  @include respond-to(sm) {
    padding: 6px 10px;
    font-size: var(--font-size-xs);
  }

  /* 按钮组在移动设备上垂直排列 */
  &.el-button-group {
    @include respond-to(md) {
      .el-button {
        display: block;
        width: 100%;
        margin: 0 0 var(--spacing-xs) 0;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* 输入框组件响应式 */
.el-input {
  @include respond-to(md) {
    .el-input__wrapper {
      padding: 6px 8px;
      font-size: var(--font-size-sm);
    }
  }

  @include respond-to(sm) {
    .el-input__wrapper {
      padding: 5px 6px;
      font-size: var(--font-size-xs);
    }
  }
}

/* 选择器组件响应式 */
.el-select {
  @include respond-to(md) {
    width: 100% !important;
    .el-input__wrapper {
      font-size: var(--font-size-sm);
    }
  }
}

/* 日期选择器响应式 */
.el-date-editor {
  @include respond-to(md) {
    width: 100% !important;
  }
}

/* 表格组件响应式 */
.el-table {
  @include responsive-table;

  /* 表格容器优化 */
  .el-table__inner-wrapper {
    overflow-x: auto !important;
  }

  /* 大屏幕表格优化 */
  @include respond-above(xl) {
    .el-table__header th,
    .el-table__body td {
      padding: 12px 8px !important;
      font-size: var(--font-size-md) !important;
    }
  }

  /* 中等屏幕表格优化 */
  @include respond-between(768px, 1199px) {
    .el-table__header th,
    .el-table__body td {
      padding: 10px 6px !important;
      font-size: var(--font-size-sm) !important;
    }

    /* 隐藏次要列 */
    .el-table__column:nth-child(n+5) {
      display: none !important;
    }
  }

  /* 移动设备表格优化 */
  @include respond-to(md) {
    border-radius: 8px !important;
    overflow: hidden !important;

    .el-table__header-wrapper,
    .el-table__body-wrapper {
      overflow-x: auto !important;
      -webkit-overflow-scrolling: touch !important;
    }

    .el-table__header {
      th {
        padding: 8px 4px !important;
        font-size: var(--font-size-xs) !important;
        white-space: nowrap !important;
        background: var(--el-fill-color-light) !important;
      }
    }

    .el-table__body {
      td {
        padding: 8px 4px !important;
        font-size: var(--font-size-xs) !important;
        white-space: nowrap !important;
      }
    }

    .el-table__row {
      .el-table__cell {
        min-width: 80px !important;

        /* 第一列固定 */
        &:first-child {
          position: sticky !important;
          left: 0 !important;
          background-color: var(--el-table-bg-color) !important;
          z-index: 2 !important;
          box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1) !important;
        }

        /* 操作列固定在右侧 */
        &:last-child {
          position: sticky !important;
          right: 0 !important;
          background-color: var(--el-table-bg-color) !important;
          z-index: 2 !important;
          box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
        }
      }
    }

    /* 隐藏不重要的列 */
    .el-table__column:nth-child(n+4):not(:last-child) {
      display: none !important;
    }
  }

  /* 小屏幕表格进一步优化 */
  @include respond-to(sm) {
    .el-table__header th,
    .el-table__body td {
      padding: 6px 2px !important;
      font-size: 11px !important;
    }

    /* 只显示前两列和操作列 */
    .el-table__column:nth-child(n+3):not(:last-child) {
      display: none !important;
    }

    .el-table__row {
      .el-table__cell {
        min-width: 60px !important;
      }
    }
  }
}

/* 分页组件响应式 */
.el-pagination {
  @include respond-to(md) {
    flex-wrap: wrap !important;
    justify-content: center !important;
    .el-pagination__total,
    .el-pagination__jump {
      display: none !important;
    }
    .el-pagination__sizes {
      margin: 0 4px !important;
    }
    .el-pager {
      .number {
        min-width: 28px !important;
        height: 28px !important;
        font-size: var(--font-size-xs) !important;
        line-height: 28px !important;
      }
    }
  }

  @include respond-to(sm) {
    .el-pagination__sizes {
      display: none !important;
    }
    .el-pager {
      .number {
        min-width: 24px !important;
        height: 24px !important;
        font-size: 11px !important;
        line-height: 24px !important;
      }
    }
  }
}

/* 弹窗组件响应式 */
.el-dialog {
  @include responsive-dialog;
}

/* 抽屉组件响应式 */
.el-drawer {
  /* 大屏幕优化 */
  @include respond-above(xl) {
    &.el-drawer--rtl,
    &.el-drawer--ltr {
      width: 500px !important;
    }
  }

  /* 中等屏幕优化 */
  @include respond-between(768px, 1199px) {
    &.el-drawer--rtl,
    &.el-drawer--ltr {
      width: 400px !important;
    }
  }

  /* 移动设备优化 */
  @include respond-to(md) {
    &.el-drawer--rtl,
    &.el-drawer--ltr {
      width: 90% !important;
      max-width: 350px !important;
    }

    .el-drawer__header {
      padding: var(--spacing-md) !important;
      border-bottom: 1px solid var(--el-border-color-lighter) !important;

      .el-drawer__title {
        font-size: var(--font-size-md) !important;
        font-weight: 600 !important;
      }

      .el-drawer__close-btn {
        font-size: 18px !important;
      }
    }

    .el-drawer__body {
      padding: var(--spacing-md) !important;
      overflow-y: auto !important;
    }
  }

  /* 小屏幕全屏显示 */
  @include respond-to(sm) {
    &.el-drawer--rtl,
    &.el-drawer--ltr {
      width: 100% !important;
      max-width: none !important;
    }

    .el-drawer__header {
      padding: var(--spacing-sm) !important;
      position: sticky !important;
      top: 0 !important;
      background: var(--el-bg-color) !important;
      z-index: 10 !important;
    }

    .el-drawer__body {
      padding: var(--spacing-sm) !important;
      height: calc(100vh - 60px) !important;
      overflow-y: auto !important;
    }
  }
}

/* 卡片组件响应式 */
.el-card {
  @include responsive-card;
}

/* 表单组件响应式 */
.el-form {
  @include responsive-form;
}

/* 标签页组件响应式 */
.el-tabs {
  @include respond-to(md) {
    .el-tabs__header {
      margin-bottom: 8px !important;
    }
    .el-tabs__item {
      height: 36px !important;
      padding: 0 12px !important;
      font-size: var(--font-size-sm) !important;
      line-height: 36px !important;
    }
  }

  @include respond-to(sm) {
    .el-tabs__item {
      height: 32px !important;
      padding: 0 8px !important;
      font-size: var(--font-size-xs) !important;
      line-height: 32px !important;
    }
  }
}

/* 下拉菜单响应式 */
.el-dropdown-menu {
  @include respond-to(md) {
    min-width: 140px !important;
    .el-dropdown-menu__item {
      padding: 8px 12px !important;
      font-size: var(--font-size-sm) !important;
    }
  }

  @include respond-to(sm) {
    min-width: 120px !important;
    .el-dropdown-menu__item {
      padding: 6px 10px !important;
      font-size: var(--font-size-xs) !important;
    }
  }
}

/* 消息提示响应式 */
.el-message {
  @include respond-to(md) {
    min-width: 280px !important;
    max-width: 90% !important;
    font-size: var(--font-size-sm) !important;
  }

  @include respond-to(sm) {
    min-width: 240px !important;
    font-size: var(--font-size-xs) !important;
  }
}

/* 工具提示响应式 */
.el-tooltip__popper {
  @include respond-to(md) {
    max-width: 200px !important;
    font-size: var(--font-size-sm) !important;
  }

  @include respond-to(sm) {
    max-width: 160px !important;
    font-size: var(--font-size-xs) !important;
  }
}

/* 步骤条响应式 */
.el-steps {
  @include respond-to(md) {
    .el-step__title {
      font-size: var(--font-size-sm) !important;
    }
    .el-step__description {
      font-size: var(--font-size-xs) !important;
    }
  }

  @include respond-to(sm) {
    .el-step__title {
      font-size: var(--font-size-xs) !important;
    }
    .el-step__description {
      display: none !important;
    }
  }
}

/* 时间线响应式 */
.el-timeline {
  @include respond-to(md) {
    .el-timeline-item__timestamp {
      font-size: var(--font-size-xs) !important;
    }
    .el-timeline-item__content {
      font-size: var(--font-size-sm) !important;
    }
  }
}

/* 树形控件响应式 */
.el-tree {
  @include respond-to(md) {
    .el-tree-node__content {
      height: 28px !important;
    }
    .el-tree-node__label {
      font-size: var(--font-size-sm) !important;
    }
  }

  @include respond-to(sm) {
    .el-tree-node__content {
      height: 26px !important;
    }
    .el-tree-node__label {
      font-size: var(--font-size-xs) !important;
    }
  }
}

/* 菜单组件响应式 */
.el-menu {
  @include respond-to(md) {
    .el-menu-item,
    .el-sub-menu__title {
      height: 44px !important;
      font-size: var(--font-size-sm) !important;
      line-height: 44px !important;
    }
  }

  @include respond-to(sm) {
    .el-menu-item,
    .el-sub-menu__title {
      height: 40px !important;
      font-size: var(--font-size-xs) !important;
      line-height: 40px !important;
    }
  }
}

/* 面包屑响应式 */
.el-breadcrumb {
  @include respond-to(md) {
    display: none !important;
  }
}

/* 标签响应式 */
.el-tag {
  @include respond-to(md) {
    height: 20px !important;
    padding: 0 6px !important;
    font-size: var(--font-size-xs) !important;
    line-height: 20px !important;
  }

  @include respond-to(sm) {
    height: 18px !important;
    padding: 0 4px !important;
    font-size: 11px !important;
    line-height: 18px !important;
  }
}

/* 徽章响应式 */
.el-badge {
  @include respond-to(sm) {
    .el-badge__content {
      min-width: 14px !important;
      height: 14px !important;
      font-size: 10px !important;
      line-height: 14px !important;
    }
  }
}
