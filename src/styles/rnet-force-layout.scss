/* 异地组网页面强制布局样式 - 最高优先级 */

/* 强制桌面端水平布局 - 覆盖所有可能的冲突样式 */
.main-box.rnet-page {
  display: flex !important;
  flex-direction: row !important;
  gap: 20px !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;
  width: 100% !important;
  min-height: 600px !important;
  padding: 20px !important;
  box-sizing: border-box !important;
  
  /* TreeFilter 强制固定宽度 */
  > .tree-filter,
  > .filter {
    flex: 0 0 220px !important;
    width: 220px !important;
    min-width: 220px !important;
    max-width: 220px !important;
    height: auto !important;
    order: 1 !important;
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    position: relative !important;
    z-index: 2 !important;
  }
  
  /* 主内容区域强制占据剩余空间 */
  > .main-content {
    flex: 1 !important;
    min-width: 0 !important;
    width: auto !important;
    height: auto !important;
    min-height: 500px !important;
    order: 2 !important;
    display: block !important;
    overflow: visible !important;
    position: relative !important;
    z-index: 1 !important;
    background-color: var(--el-bg-color) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    border: 1px solid var(--el-border-color-light) !important;
    box-shadow: 0 0 12px rgb(0 0 0 / 5%) !important;
  }
}

/* 桌面端强制样式 */
@media screen and (min-width: 768px) {
  .main-box.rnet-page {
    flex-direction: row !important;
    
    > .tree-filter,
    > .filter {
      width: 220px !important;
      min-width: 220px !important;
      max-width: 220px !important;
      order: 1 !important;
      margin-right: 0 !important;
      margin-bottom: 0 !important;
    }
    
    > .main-content {
      flex: 1 !important;
      order: 2 !important;
      min-width: 0 !important;
    }
  }
}

/* 中等屏幕优化 */
@media screen and (min-width: 992px) {
  .main-box.rnet-page {
    gap: 24px !important;
    padding: 24px !important;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 1200px) {
  .main-box.rnet-page {
    gap: 30px !important;
    padding: 30px !important;
  }
}

/* 移动端垂直布局 - 适用于所有移动设备 */
@media screen and (max-width: 767px) {
  .main-box.rnet-page {
    flex-direction: column !important;
    gap: 16px !important;
    padding: 16px !important;
    align-items: stretch !important;
    min-height: auto !important;

    > .tree-filter,
    > .filter {
      flex: none !important;
      width: 100% !important;
      min-width: auto !important;
      max-width: none !important;
      order: 2 !important;
      margin-right: 0 !important;
      margin-bottom: 0 !important;
      height: auto !important;
    }

    > .main-content {
      flex: none !important;
      order: 1 !important;
      width: 100% !important;
      min-width: auto !important;
      min-height: 400px !important;
      padding: 16px !important;
    }
  }
}

/* 小屏幕进一步优化 */
@media screen and (max-width: 575px) {
  .main-box.rnet-page {
    gap: 12px !important;
    padding: 12px !important;
    border-radius: 8px !important;

    > .main-content {
      min-height: 300px !important;
      padding: 12px !important;
    }
  }
}

/* 确保内部元素正确显示 */
.main-box.rnet-page {
  .main-content {
    .box-card {
      width: 100% !important;
      height: auto !important;
      min-height: 400px !important;
      
      .el-card__header {
        display: block !important;
        width: 100% !important;
        
        .clearfix {
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
          width: 100% !important;
        }
      }
      
      .el-card__body {
        display: block !important;
        width: 100% !important;
        min-height: 300px !important;
      }
    }
  }
}

/* 移动端调试样式 - 可以在开发时启用 */
/*
@media screen and (max-width: 767px) {
  .main-box.rnet-page {
    border: 3px solid red !important;
    background-color: rgba(255, 0, 0, 0.1) !important;

    > .tree-filter,
    > .filter {
      border: 2px solid blue !important;
      background-color: rgba(0, 0, 255, 0.2) !important;

      &::before {
        content: "TreeFilter - Order: 2" !important;
        display: block !important;
        background: blue !important;
        color: white !important;
        padding: 4px !important;
        font-size: 12px !important;
      }
    }

    > .main-content {
      border: 2px solid green !important;
      background-color: rgba(0, 255, 0, 0.2) !important;

      &::before {
        content: "Main Content - Order: 1" !important;
        display: block !important;
        background: green !important;
        color: white !important;
        padding: 4px !important;
        font-size: 12px !important;
      }
    }
  }
}
*/
