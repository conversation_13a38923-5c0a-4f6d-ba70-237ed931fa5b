import { ref, computed, onMounted, onUnmounted, readonly } from "vue";
import { useDebounceFn } from "@vueuse/core";

export interface ResponsiveInfo {
  screenWidth: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLarge: boolean;
  breakpoint: "mobile" | "tablet" | "desktop" | "large";
}

/**
 * 响应式布局Hook
 * 提供屏幕尺寸检测和响应式状态管理
 */
export const useResponsive = () => {
  const screenWidth = ref(0);

  // 断点定义
  const breakpoints = {
    mobile: 768,
    tablet: 992,
    desktop: 1200,
    large: 1440
  };

  // 响应式状态计算
  const isMobile = computed(() => screenWidth.value <= breakpoints.mobile);
  const isTablet = computed(() => screenWidth.value > breakpoints.mobile && screenWidth.value <= breakpoints.tablet);
  const isDesktop = computed(() => screenWidth.value > breakpoints.tablet && screenWidth.value <= breakpoints.desktop);
  const isLarge = computed(() => screenWidth.value > breakpoints.desktop);

  // 当前断点
  const breakpoint = computed((): "mobile" | "tablet" | "desktop" | "large" => {
    if (isMobile.value) return "mobile";
    if (isTablet.value) return "tablet";
    if (isDesktop.value) return "desktop";
    return "large";
  });

  // 响应式信息对象
  const responsiveInfo = computed(
    (): ResponsiveInfo => ({
      screenWidth: screenWidth.value,
      isMobile: isMobile.value,
      isTablet: isTablet.value,
      isDesktop: isDesktop.value,
      isLarge: isLarge.value,
      breakpoint: breakpoint.value
    })
  );

  // 更新屏幕宽度
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth;
  };

  // 防抖的resize处理器
  const debouncedResize = useDebounceFn(updateScreenWidth, 100);

  // 初始化
  onMounted(() => {
    updateScreenWidth();
    window.addEventListener("resize", debouncedResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", debouncedResize);
  });

  return {
    screenWidth: readonly(screenWidth),
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),
    isLarge: readonly(isLarge),
    breakpoint: readonly(breakpoint),
    responsiveInfo: readonly(responsiveInfo),
    breakpoints
  };
};

/**
 * 移动端菜单控制Hook
 */
export const useMobileMenu = () => {
  const showMobileMenu = ref(false);

  const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
  };

  const openMobileMenu = () => {
    showMobileMenu.value = true;
  };

  const closeMobileMenu = () => {
    showMobileMenu.value = false;
  };

  return {
    showMobileMenu: readonly(showMobileMenu),
    toggleMobileMenu,
    openMobileMenu,
    closeMobileMenu
  };
};

/**
 * 设备类型检测Hook
 */
export const useDeviceDetection = () => {
  const { responsiveInfo } = useResponsive();

  // 设备类型判断
  const deviceType = computed(() => {
    const { isMobile, isTablet } = responsiveInfo.value;
    if (isMobile) return "mobile";
    if (isTablet) return "tablet";
    return "desktop";
  });

  // 是否为触摸设备
  const isTouchDevice = computed(() => {
    return "ontouchstart" in window || navigator.maxTouchPoints > 0;
  });

  // 获取设备信息
  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /mobile|android|iphone|ipod|phone|blackberry|iemobile|opera mini/.test(userAgent);
    const isTabletDevice = /ipad|tablet|playbook|silk/.test(userAgent);

    return {
      userAgent,
      isMobileDevice,
      isTabletDevice,
      isPCDevice: !isMobileDevice && !isTabletDevice,
      isTouchDevice: isTouchDevice.value,
      deviceType: deviceType.value
    };
  };

  return {
    deviceType: readonly(deviceType),
    isTouchDevice: readonly(isTouchDevice),
    getDeviceInfo
  };
};
