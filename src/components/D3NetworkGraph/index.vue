<template>
  <div class="d3-network-graph" ref="container">
    <!-- 使用浮动按钮组件 -->
    <FloatingButtons @reset-zoom="resetZoom" />
    <div class="legend">
      <div class="legend-item">
        <span class="legend-line success"></span>
        <span>{{ $t("device.rnetStatusSuccess") }}</span>
      </div>
      <div class="legend-item">
        <span class="legend-line process"></span>
        <span>{{ $t("device.rnetStatusConnecting") }}</span>
      </div>
      <div class="legend-item">
        <span class="legend-line failed"></span>
        <span>{{ $t("device.rnetStatusFail") }}</span>
      </div>
    </div>
    <div v-if="showDeviceInfo" class="device-info-popup" :style="popupStyle">
      <div class="popup-header">
        <span>{{ $t("device.deviceDetail") }}</span>
        <div class="popup-header-buttons">
          <el-button type="primary" circle size="small" @click="refreshDeviceInfo">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button type="danger" circle size="small" @click="closeDeviceInfo">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="popup-content">
        <div class="card-content">
          <strong>
            {{ selectedDevice.deviceId }}
            {{
              selectedDevice.name && selectedDevice.name.trim() && selectedDevice.name !== selectedDevice.deviceId
                ? "(" + selectedDevice.name + ")"
                : ""
            }}
          </strong>
        </div>
        <el-row>
          <el-col>
            <span>
              <el-text :class="selectedDevice.status == 0 ? 'online-text' : 'offline-text'">
                • {{ selectedDevice.status == 0 ? $t("common.onLine") : $t("common.offLine") }}
              </el-text>
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="info-label">{{ $t("topology.deviceInfo.type") }}:</div>
            <div class="info-value">
              {{
                selectedDevice.deviceType
                  ? isChinese
                    ? getDeviceTypeName(selectedDevice.deviceType)
                    : selectedDevice.deviceType.toUpperCase()
                  : "-"
              }}
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="info-label">{{ $t("topology.deviceInfo.serialNumber") }}:</div>
            <div class="info-value">{{ selectedDevice.deviceId || "-" }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="info-label">{{ $t("common.networkType") }}:</div>
            <div class="info-value">
              {{
                selectedDevice?.data?.system?.rNet?.stun === 0
                  ? "CODE"
                  : selectedDevice?.data?.system?.rNet?.stun === 1
                    ? "SYMMETRIC"
                    : "---"
              }}
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="info-label">{{ $t("common.mappingIP") }}:</div>
            <div class="info-value">
              {{ selectedDevice?.data?.system?.rNet?.wan ? selectedDevice?.data?.system?.rNet?.wan : "---" }} ->
              {{ selectedDevice?.data?.system?.rNet?.map ? selectedDevice?.data?.system?.rNet?.map : "---" }}
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <div class="info-label">{{ $t("common.allowedAccessIPSegment") }}:</div>
            <div class="info-value">
              {{ selectedDevice?.data?.system?.rNet?.allowedIPs ? selectedDevice?.data?.system?.rNet?.allowedIPs : "---" }}
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 移除内联的浮动按钮，使用外部组件 -->
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, inject } from "vue";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import * as d3 from "d3";
import { getDeviceType } from "@/api/modules/project";

useI18n(); // 使用 I18n，但在模板中通过 $t 访问
const globalStore = useGlobalStore();
const isDark = ref(globalStore.isDark);
const isChinese = ref(globalStore.language === "zh");

// 注入设备类型数据
const deviceTypes = ref([]);
const injectedDeviceTypes = inject("deviceTypes", []);

// 确保 deviceTypes 是响应式的
if (Array.isArray(injectedDeviceTypes)) {
  deviceTypes.value = injectedDeviceTypes;
} else {
  console.warn("Injected deviceTypes is not an array:", injectedDeviceTypes);
}

// 辅助函数：获取设备类型名称
const getDeviceTypeName = deviceType => {
  if (!deviceType) return "-";

  // 输出调试信息
  console.log("Getting device type name for:", deviceType);
  console.log("Available device types:", deviceTypes.value);

  // 转换为大写进行比较
  const upperDeviceType = deviceType.toUpperCase();

  // 在 deviceTypes 中查找匹配的类型
  const matchedType = deviceTypes.value.find(type => {
    const configCode = type.configCode?.toUpperCase() || "";
    const isMatch = configCode === upperDeviceType;
    if (isMatch) {
      console.log("Found matching device type:", type);
    }
    return isMatch;
  });

  // 返回匹配的类型名称或设备类型本身
  const result = matchedType?.configDesc || upperDeviceType;
  console.log("Device type name result:", result);
  return result;
};

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ nodes: [], links: [] })
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  }
});

const emit = defineEmits(["nodeClick", "refresh"]);

// 容器引用
const container = ref(null);
let svg = null;
let simulation = null;

// 设备信息弹出相关状态
const selectedDevice = ref({});
const showDeviceInfo = ref(false);

// 计算弹窗位置样式
const popupStyle = ref({
  left: "0px",
  top: "0px"
});

// 关闭设备信息弹窗
const closeDeviceInfo = () => {
  showDeviceInfo.value = false;
};

// 刷新设备信息
const refreshDeviceInfo = async () => {
  // 触发刷新事件
  emit("refresh", selectedDevice.value);

  // 同时刷新设备类型数据
  try {
    const response = await getDeviceType();
    if (response && response.data) {
      deviceTypes.value = Array.isArray(response.data) ? response.data : [];
      console.log("Refreshed deviceTypes:", deviceTypes.value);
    }
  } catch (error) {
    console.error("Failed to refresh device types:", error);
  }
};

// 处理节点点击事件
const handleNodeClick = (event, d) => {
  event.stopPropagation();

  // 如果是根节点（互联网），不显示设备信息
  if (d.isRoot) return;

  // 设置选中的设备
  selectedDevice.value = d;

  // 调试信息
  console.log("Selected device:", d);
  console.log("Device type:", d.deviceType);
  console.log("Device types array:", deviceTypes.value);
  console.log("Is Chinese:", isChinese.value);

  // 测试设备类型转换
  if (d.deviceType) {
    console.log("Device type in handleNodeClick:", d.deviceType);
    console.log("DeviceTypes in handleNodeClick:", deviceTypes.value);

    // 尝试手动转换
    if (deviceTypes.value.length > 0) {
      const matchedType = deviceTypes.value.find(type => {
        const configCode = type.configCode?.toUpperCase() || "";
        const deviceTypeUpper = d.deviceType?.toUpperCase() || "";
        const isMatch = configCode === deviceTypeUpper;
        console.log(`Comparing ${configCode} with ${deviceTypeUpper}: ${isMatch}`);
        return isMatch;
      });
      console.log("Matched device type:", matchedType);
    } else {
      console.warn("DeviceTypes array is empty in handleNodeClick");
    }
  }

  // 计算弹窗位置
  const rect = container.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  // 设置弹窗位置，避免超出边界
  popupStyle.value = {
    left: `${Math.min(x, props.width - 300)}px`,
    top: `${Math.min(y, props.height - 300)}px`
  };

  // 显示弹窗
  showDeviceInfo.value = true;

  // 触发节点点击事件
  emit("nodeClick", d);
};

// 重置缩放
const resetZoom = () => {
  if (!container.value) return;

  // 重置缩放比例
  if (zoom) {
    d3.select(container.value).transition().duration(750).call(zoom.transform, d3.zoomIdentity);
  }
};

// 缩放行为
let zoom = null;

// 更新粒子动画
const updateParticles = (flowParticles, particles, nodePositions) => {
  if (!flowParticles) return;

  // 输出调试信息，确认粒子更新函数被调用
  console.log("Updating particles...");

  flowParticles.each(function (d, i) {
    // 处理 source 和 target 可能是对象或字符串的情况
    const sourceId = typeof d.source === "object" ? d.source.id : d.source;
    const targetId = typeof d.target === "object" ? d.target.id : d.target;

    const sourcePos = nodePositions.get(sourceId);
    const targetPos = nodePositions.get(targetId);

    if (!sourcePos || !targetPos) {
      console.log(`Missing position for source ${sourceId} or target ${targetId}`);
      return;
    }

    // 初始化粒子如果还没有初始化
    if (!particles[i]) {
      // 使用小的初始值，确保从起点开始
      particles[i] = { t: 0, speed: 0.002 };
    }

    // 更新粒子位置，匀速运动
    particles[i].t += particles[i].speed; // 使用更小的速度增量，使运动更平滑

    // 当粒子到达终点时，重置到起点，实现循环运动
    if (particles[i].t >= 1) {
      particles[i].t = 0;
    }

    const t = particles[i].t;

    // 根据方向决定粒子流动方向
    let sourceX, sourceY, targetX, targetY;
    if (d.direction === "reverse") {
      sourceX = targetPos.x;
      sourceY = targetPos.y;
      targetX = sourcePos.x;
      targetY = sourcePos.y;
    } else {
      sourceX = sourcePos.x;
      sourceY = sourcePos.y;
      targetX = targetPos.x;
      targetY = targetPos.y;
    }

    // 计算粒子当前位置
    const x = sourceX + (targetX - sourceX) * t;
    const y = sourceY + (targetY - sourceY) * t;

    // 更新粒子位置
    d3.select(this).attr("cx", x).attr("cy", y);
  });
};

// 初始化图表
const initGraph = () => {
  if (!container.value || !props.data || !props.data.nodes || !props.data.nodes.length) return;

  // 清除旧图表
  d3.select(container.value).select("svg").remove();

  // 创建SVG
  svg = d3
    .select(container.value)
    .append("svg")
    .attr("width", props.width)
    .attr("height", props.height)
    .attr("viewBox", [0, 0, props.width, props.height])
    .on("click", () => {
      // 点击空白区域关闭设备信息弹出
      showDeviceInfo.value = false;
    });

  // 设置容器为相对定位，使浮动按钮可以相对定位
  d3.select(container.value).style("position", "relative");

  // 添加灰度滤镜定义，用于离线节点
  const defs = svg.append("defs");
  const grayscaleFilter = defs
    .append("filter")
    .attr("id", "grayscale")
    .attr("x", "0")
    .attr("y", "0")
    .attr("width", "100%")
    .attr("height", "100%");

  grayscaleFilter
    .append("feColorMatrix")
    .attr("type", "matrix")
    .attr("values", "0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0");

  // 创建主容器
  const g = svg.append("g");

  // 添加缩放功能
  zoom = d3
    .zoom()
    .scaleExtent([0.1, 4])
    .on("zoom", event => {
      g.attr("transform", event.transform);
    });

  svg.call(zoom);

  // 创建连线
  const link = g
    .append("g")
    .attr("stroke-opacity", 0.6)
    .selectAll("line")
    .data(props.data.links)
    .join("line")
    .attr("stroke", d => {
      switch (d.status) {
        case "SUCCESS":
          return "#00C6FF";
        case "CONNECTING":
          return "#FFBF00";
        case "FAIL":
          return "#FF0000";
        default:
          return "#00C6FF";
      }
    })
    .attr("stroke-width", 2)
    .attr("stroke-dasharray", d => (d.medium === "RADIO" ? "5,5" : "none"));

  // 输出连线数据以便调试
  console.log("Links data:", props.data.links);

  // 不使用箭头，而是使用流动粒子来表示方向

  // 创建节点
  const node = g
    .append("g")
    .selectAll(".node")
    .data(props.data.nodes)
    .join("g")
    .attr("class", "node")
    .style("cursor", "pointer")
    .on("click", handleNodeClick);

  // 添加节点图标
  node
    .append("image")
    .attr("xlink:href", d => d.symbol)
    .attr("width", 28) // 缩小图标大小
    .attr("height", 28) // 缩小图标大小
    .attr("x", -14) // 调整偏移量以保持居中
    .attr("y", -14) // 调整偏移量以保持居中
    .attr("filter", d => (d.status === 1 ? "url(#grayscale)" : null));

  // 添加节点标签
  node
    .append("text")
    .attr("dy", 26) // 进一步减小与图标的距离
    .attr("text-anchor", "middle")
    .attr("fill", isDark.value ? "#ffffff" : "#333333")
    .style("font-size", "11px") // 缩小字体大小
    .style("font-weight", "bold")
    .text(d => d.name || d.deviceId);

  // 添加节点速率标签
  node
    .append("text")
    .attr("dy", 38) // 进一步减小与名称标签的距离
    .attr("text-anchor", "middle")
    .attr("fill", isDark.value ? "#00C6FF" : "#0066CC")
    .style("font-size", "9px") // 缩小字体大小
    .style("font-weight", "bold")
    .text(d => d.rate || "");

  // 使用固定布局而不是力导向图
  // 计算节点位置
  const nodeCount = props.data.nodes.length;
  // 减小半径比例，使节点更靠近中心
  const radius = Math.min(props.width, props.height) * 0.35;
  const centerX = props.width / 2;
  // 将中心点向上移动一点，留出更多空间给底部节点
  const centerY = props.height / 2 - 20;

  // 创建节点位置的副本，避免直接修改 props
  const nodePositions = new Map();

  // 如果节点数量小于等于 1，则将节点放在中心
  if (nodeCount <= 1) {
    if (props.data.nodes.length === 1) {
      const nodeId = props.data.nodes[0].id;
      nodePositions.set(nodeId, { x: centerX, y: centerY });
    }
  } else {
    // 如果节点数量大于 1，则将节点分布在圆周上
    // 计算节点分布的起始角度，避免节点集中在底部
    const startAngle = -Math.PI / 2; // 从顶部开始

    props.data.nodes.forEach((node, i) => {
      // 根据节点数量和位置调整半径
      let adjustedRadius = radius;

      if (nodeCount > 6) {
        // 如果节点数量较多，使用稍微小一点的半径
        adjustedRadius = radius * 0.85;

        // 对于底部的节点，进一步减小半径，使其更靠近中心
        const normalizedPosition = i / nodeCount;
        if (normalizedPosition > 0.6 && normalizedPosition < 0.9) {
          // 底部节点的半径再减小一点
          adjustedRadius = radius * 0.8;
        }
      }

      // 计算角度，从起始角度开始
      const angle = startAngle + (i / nodeCount) * 2 * Math.PI;
      const x = centerX + adjustedRadius * Math.cos(angle);
      const y = centerY + adjustedRadius * Math.sin(angle);
      nodePositions.set(node.id, { x, y });
    });
  }

  // 更新连线位置
  link
    .attr("x1", d => {
      // 处理 source 可能是对象或字符串的情况
      const sourceId = typeof d.source === "object" ? d.source.id : d.source;
      const pos = nodePositions.get(sourceId) || { x: 0, y: 0 };
      return pos.x;
    })
    .attr("y1", d => {
      const sourceId = typeof d.source === "object" ? d.source.id : d.source;
      const pos = nodePositions.get(sourceId) || { x: 0, y: 0 };
      return pos.y;
    })
    .attr("x2", d => {
      const targetId = typeof d.target === "object" ? d.target.id : d.target;
      const pos = nodePositions.get(targetId) || { x: 0, y: 0 };
      return pos.x;
    })
    .attr("y2", d => {
      const targetId = typeof d.target === "object" ? d.target.id : d.target;
      const pos = nodePositions.get(targetId) || { x: 0, y: 0 };
      return pos.y;
    });

  // 更新节点位置
  node.attr("transform", d => {
    const pos = nodePositions.get(d.id) || { x: 0, y: 0 };
    return `translate(${pos.x},${pos.y})`;
  });

  // 我们将在后面创建模拟对象

  // 添加数据流动画效果
  // 使用所有连线，每个连线只生成一个粒子
  const originalLinks = props.data.links; // 使用所有连线

  // 如果没有连线，则跳过粒子创建
  if (!originalLinks || originalLinks.length === 0) {
    console.log("No links available for particles");
    return;
  }

  // 输出连线数据以便调试
  console.log(`连线数量: ${originalLinks.length}`);

  const flowParticles = g
    .append("g")
    .selectAll(".flow-particle")
    .data(originalLinks)
    .join("circle")
    .attr("class", "flow-particle")
    .attr("r", 4) // 增大粒子大小为4，更易见
    .attr("fill", d => {
      switch (d.status) {
        case "SUCCESS":
          return "#00C6FF";
        case "CONNECTING":
          return "#FFBF00";
        case "FAIL":
          return "#FF0000";
        default:
          return "#00C6FF";
      }
    })
    .style("opacity", 1); // 增大不透明度为1，更易见

  // 初始化粒子位置
  const particles = {};
  originalLinks.forEach((d, i) => {
    // 初始化为起点或小的初始值，使用更小的速度使运动更平滑
    particles[i] = { t: 0, speed: 0.002 };
  });

  // 创建模拟对象，用于触发粒子动画
  // 先停止之前的动画，防止内存泄漏
  if (simulation && typeof simulation.stop === "function") {
    simulation.stop();
  }

  // 使用 requestAnimationFrame 来实现更平滑的动画
  let animationFrameId = null;
  let isAnimating = false;

  simulation = {
    on: function (event, callback) {
      if (event === "tick") {
        // 停止之前的动画
        this.stop();

        // 使用 requestAnimationFrame 实现平滑动画
        isAnimating = true;

        const animate = () => {
          if (!isAnimating) return;

          // 调用粒子更新函数
          callback(flowParticles, particles, nodePositions);

          // 继续下一帧动画
          animationFrameId = requestAnimationFrame(animate);
        };

        // 开始动画
        animate();
      }
      return this;
    },
    stop: function () {
      // 停止动画
      isAnimating = false;
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }
      return this;
    }
  };

  // 启动粒子动画
  simulation.on("tick", updateParticles);
};

// 在 setup 中添加
const updateNodesAndLinks = (nodes, links) => {
  // 更新节点状态和流量
  nodes.forEach(node => {
    const nodeElement = d3.select(`#node-${node.id}`);
    if (nodeElement.size() > 0) {
      // 更新节点状态
      nodeElement.select(".node-status").attr("class", `node-status ${node.status === 0 ? "online" : "offline"}`);

      // 更新节点流量
      nodeElement.select(".node-rate").text(node.rate);
    }
  });

  // 更新连接状态和流量
  links.forEach(link => {
    const linkElement = d3.select(`#link-${link.source}-${link.target}`);
    if (linkElement.size() > 0) {
      // 更新连接状态和方向
      linkElement
        .attr("class", `link ${link.direction}`)
        .select(".link-flow")
        .text(`${formatBytes(link.rxByte)} / ${formatBytes(link.txByte)}`);
    }
  });
};

// 暴露方法给父组件
defineExpose({
  updateNodesAndLinks
});

// 监听数据变化
watch(
  () => props.data,
  () => {
    initGraph();
  },
  { deep: true }
);

// 监听主题变化
watch(
  () => globalStore.isDark,
  newVal => {
    isDark.value = newVal;
    initGraph();
  }
);

// 监听语言变化
watch(
  () => globalStore.language,
  newVal => {
    isChinese.value = newVal === "zh";
  }
);

// 监听窗口大小变化
const handleResize = () => {
  initGraph();
};

onMounted(async () => {
  // 输出调试信息
  console.log("D3NetworkGraph mounted");

  // 直接获取设备类型数据
  try {
    const response = await getDeviceType();
    if (response && response.data) {
      deviceTypes.value = Array.isArray(response.data) ? response.data : [];
      console.log("Fetched deviceTypes:", deviceTypes.value);
    }
  } catch (error) {
    console.error("Failed to fetch device types:", error);
  }

  // 初始化图表
  initGraph();
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  // 确保清理所有定时器
  if (simulation && typeof simulation.stop === "function") {
    console.log("Stopping simulation and clearing timers");
    simulation.stop();
  }
});
</script>

<style scoped>
.d3-network-graph {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: v-bind(
    'isDark ? "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)" : "linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%)"'
  );
  border-radius: 8px;
  box-shadow: v-bind('isDark ? "0 8px 24px rgba(0, 0, 0, 0.3)" : "0 8px 24px rgba(0, 0, 0, 0.1)"');
}
.legend {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 100;
  padding: 12px 15px;
  font-weight: 500;
  background-color: v-bind('isDark ? "rgba(30, 30, 30, 0.7)" : "rgba(255, 255, 255, 0.7)"');
  backdrop-filter: blur(10px);
  border: 1px solid v-bind('isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)"');
  border-radius: 8px;
  box-shadow: v-bind('isDark ? "0 4px 12px rgba(0, 0, 0, 0.3)" : "0 4px 12px rgba(0, 0, 0, 0.1)"');
}
.legend-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  margin-bottom: 8px;
}
.legend-item:last-child {
  margin-bottom: 0;
}
.legend-line {
  width: 30px;
  height: 3px;
  margin-right: 12px;
  border-radius: 1.5px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 20%);
}
.legend-line.success {
  background-color: #00c6ff;
  box-shadow: 0 0 8px rgb(0 198 255 / 50%);
}
.legend-line.process {
  background-color: #ffbf00;
  box-shadow: 0 0 8px rgb(255 191 0 / 50%);
}
.legend-line.failed {
  background-color: #ff0000;
  box-shadow: 0 0 8px rgb(255 0 0 / 50%);
}
.device-info-popup {
  position: absolute;
  z-index: 1000;
  width: 300px;
  overflow: hidden;
  background-color: v-bind('isDark ? "rgba(26, 26, 26, 0.85)" : "rgba(255, 255, 255, 0.85)"');
  backdrop-filter: blur(10px);
  border: 1px solid v-bind('isDark ? "#666666" : "#dcdfe6"');
  border-radius: 8px;
  box-shadow: 0 6px 24px 0 v-bind('isDark ? "rgb(0 0 0 / 80%)" : "rgb(0 0 0 / 25%)"');
}
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: v-bind('isDark ? "rgba(40, 40, 40, 0.9)" : "rgba(240, 240, 240, 0.9)"');
  border-bottom: 1px solid v-bind('isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)"');
}
.popup-header-buttons {
  display: flex;
  gap: 8px;
}
.popup-content {
  max-height: 300px;
  padding: 15px;
  overflow-y: auto;
}
.card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 14px;
}
.info-label {
  display: inline-block;
  margin-right: 5px;
  font-size: 13px;
  font-weight: bold;
  color: v-bind('isDark ? "#e0e0e0" : "#606266"');
}
.info-value {
  display: inline-block;
  font-size: 13px;
  color: v-bind('isDark ? "#ffffff" : "#333333"');
  word-break: break-all;
}
.el-row {
  padding-bottom: 8px;
  margin-bottom: 8px;
  border-bottom: 1px solid v-bind('isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)"');
}
.el-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
.online-text {
  font-weight: bold;
  color: #67c23a;
}
.offline-text {
  font-weight: bold;
  color: #f56c6c;
}

/* 确保浮动按钮组件正确显示 */
:deep(.floating-buttons) {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}
</style>
