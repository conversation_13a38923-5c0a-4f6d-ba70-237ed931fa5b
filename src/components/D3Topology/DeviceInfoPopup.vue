<template>
  <div v-if="visible" class="device-info-popup" :style="position">
    <div class="popup-header">
      <span :class="{ 'dark-mode-text': isDark }">{{ t("device.deviceDetail") }}</span>
      <div class="header-actions">
        <el-button @click="close" class="action-btn close-btn" :title="t('common.close')" type="danger" size="small" circle>
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>
    <div class="popup-content">
      <div class="info-grid">
        <div class="info-item">
          <span class="label" :class="{ 'dark-mode-text': isDark }">{{ t("device.deviceName") }}:</span>
          <span class="value" :class="{ 'dark-mode-text': isDark }">{{
            deviceInfo.name || deviceInfo.deviceName || deviceInfo.deviceModel || "-"
          }}</span>
        </div>
        <div class="info-item">
          <span class="label" :class="{ 'dark-mode-text': isDark }">{{ t("device.deviceType") }}:</span>
          <span class="value" :class="{ 'dark-mode-text': isDark }">{{ getDeviceTypeLabel }}</span>
        </div>
        <div class="info-item">
          <span class="label" :class="{ 'dark-mode-text': isDark }">{{ t("device.deviceId") }}:</span>
          <span class="value" :class="{ 'dark-mode-text': isDark }">{{ getDeviceId() }}</span>
        </div>
        <div class="info-item">
          <span class="label" :class="{ 'dark-mode-text': isDark }">{{ t("device.ip") }}:</span>
          <span class="value" :class="{ 'dark-mode-text': isDark }">{{ deviceInfo.ipaddr || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label" :class="{ 'dark-mode-text': isDark }">{{ t("device.mac") }}:</span>
          <span class="value" :class="{ 'dark-mode-text': isDark }">{{ deviceInfo.mac || deviceInfo.macaddr || "-" }}</span>
        </div>
        <div class="info-item">
          <span class="label" :class="{ 'dark-mode-text': isDark }">{{ t("network.connectionType") }}:</span>
          <span class="value" :class="{ 'dark-mode-text': isDark }">{{ getConnectionType(deviceInfo.extra?.medium) }}</span>
        </div>
      </div>

      <!-- 功能按钮区域 -->
      <!-- <div v-if="hasDeviceInfo" class="action-buttons"> -->
      <div class="action-buttons">
        <!-- <el-button
          type="primary"
          size="small"
          @click="setAsExitDevice"
          :disabled="!hasDeviceInfo || (!isExitDevice && !canBeExitDevice)"
          :title="getExitDeviceButtonTitle"
        > -->
        <el-button
          type="primary"
          size="small"
          @click="setAsExitDevice"
          :disabled="!isExitDevice && !canBeExitDevice"
          :title="getExitDeviceButtonTitle"
        >
          {{ isExitDevice ? t("device.cancelExitDevice") : t("device.setAsExitDevice") }}
        </el-button>
        <el-button type="primary" size="small" @click="viewDeviceDetails" :disabled="!hasDeviceInfo">
          {{ t("device.viewDetails") }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, inject, ref } from "vue";
import { useI18n } from "vue-i18n";
import { Close } from "@element-plus/icons-vue";
import { useGlobalStore } from "@/stores/modules/global";
import type { Project } from "@/api/interface/project";

const { t, locale } = useI18n();
const globalStore = useGlobalStore();
const isDark = computed(() => globalStore.isDark);
const isChinese = computed(() => globalStore.language === "zh");

// 注入设备类型列表
const deviceTypes = inject("deviceTypes", ref<Project.ResConfigList[]>([]));

const props = defineProps({
  deviceInfo: {
    type: Object,
    default: () => ({})
  },
  visible: {
    type: Boolean,
    default: false
  },
  x: {
    type: Number,
    default: 0
  },
  y: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(["close", "setAsExitDevice", "cancelExitDevice", "viewDeviceDetails"]);

// 计算弹窗位置
const position = computed(() => {
  // 计算弹出框位置，避免超出屏幕
  const width = 380; // 弹出框宽度
  const height = 280; // 弹出框高度估计值
  const offset = 15; // 与节点的偏移量

  // 计算弹出框的水平位置
  // 将弹出框的中心对齐节点的中心，然后减去弹出框宽度的一半
  let left = props.x - width / 2;

  // 确保弹出框不会超出屏幕边界
  left = Math.max(offset, Math.min(left, window.innerWidth - width - offset));

  // 计算弹出框的垂直位置
  // 将弹出框的中心对齐节点的中心，并稍微向上偏移
  let top = props.y - height / 2 - 20; // 向上偏移20px，避免遮挡节点

  // 确保弹出框不会超出屏幕上下边界
  top = Math.max(offset, Math.min(top, window.innerHeight - height - offset));

  return {
    left: `${left}px`,
    top: `${top}px`,
    maxHeight: `${window.innerHeight - 2 * offset}px` // 确保弹窗不会超过屏幕高度
  };
});

// 关闭弹窗
const close = () => {
  emit("close");
};

// 判断是否有设备信息
const hasDeviceInfo = computed(() => {
  const deviceId = getDeviceId();
  return deviceId && deviceId !== "-";
});

// 判断设备是否可以设置为出口设备
const canBeExitDevice = computed(() => {
  // 如果设备没有canParentBeExitDevice属性，则不能设置为出口设备
  return !!props.deviceInfo.canParentBeExitDevice;
});

// 判断设备是否已经是出口设备
const isExitDevice = computed(() => {
  return !!props.deviceInfo.isExitDevice;
});

// 获取出口设备按钮的提示文本
const getExitDeviceButtonTitle = computed(() => {
  // 如果设备已经是出口设备，则显示取消出口设备的提示
  if (isExitDevice.value) {
    return t("device.cancelExitDeviceTip");
  }

  // 如果设备是二级节点（直接连接到Internet的节点）
  if (props.deviceInfo.depth === 1) {
    return t("device.cannotSetAsExitDeviceTip");
  }

  // 如果设备不能设置为出口设备
  if (!canBeExitDevice.value) {
    return t("device.cannotSetAsExitDeviceTip");
  }

  // 其他情况下显示设置出口设备的提示
  return t("device.setAsExitDeviceTip");
});

// 设为出口设备
const setAsExitDevice = () => {
  // if (!hasDeviceInfo.value) return;

  if (isExitDevice.value) {
    // 如果已经是出口设备，则取消设置
    emit("cancelExitDevice", props.deviceInfo);
  } else {
    // 否则设置为出口设备
    emit("setAsExitDevice", props.deviceInfo);
  }

  close();
};

// 查看设备详情
const viewDeviceDetails = () => {
  if (!hasDeviceInfo.value) return;
  emit("viewDeviceDetails", {
    deviceId: getDeviceId(),
    deviceName: props.deviceInfo.name || props.deviceInfo.deviceName,
    deviceType: props.deviceInfo.deviceType || props.deviceInfo.extra?.deviceType
  });
  close();
};

// 获取设备ID
const getDeviceId = () => {
  // 如果topologyType是peer或subDev，优先使用sn字段
  const topologyType = props.deviceInfo.topologyType || props.deviceInfo.extra?.topologyType;

  if (topologyType === "peer" || topologyType === "subDev") {
    // 对于peer或subDev类型，优先使用sn字段
    return props.deviceInfo.extra?.sn || props.deviceInfo.deviceId || "-";
  } else {
    // 其他类型使用deviceId
    return (
      props.deviceInfo.deviceId || props.deviceInfo.extra?.deviceId || props.deviceInfo.sn || props.deviceInfo.extra?.sn || "-"
    );
  }
};

// 获取设备类型的国际化文本
const getDeviceTypeLabel = computed(() => {
  const deviceType = props.deviceInfo.deviceType || props.deviceInfo.extra?.deviceType;
  if (!deviceType) return "-";

  const deviceTypeItem = deviceTypes.value.find(item => item.configCode === deviceType);
  return deviceTypeItem
    ? isChinese.value
      ? deviceTypeItem.configDesc
      : deviceTypeItem.attribute2 || deviceTypeItem.attribute || deviceTypeItem.configDesc
    : deviceType;
});

// 获取连接方式的国际化文本
const getConnectionType = medium => {
  // if (!medium) return "-";

  switch (medium) {
    case "CABLE":
      return t("network.wiredConnection");
    case "RADIO":
      return t("network.wirelessConnection");
    default:
      return t("network.wiredConnection");
  }
};

// 监听语言变化，强制更新组件
watch(locale, () => {
  // 语言变化时什么也不做，组件会自动响应语言变化
});
</script>

<style scoped>
/* 黑暗模式下的文字样式 */
.dark-mode-text {
  font-weight: 500 !important;

  /* 移除文字阴影效果 */
}
.device-info-popup {
  position: fixed; /* 使用fixed而不是absolute，确保始终可见 */
  top: 0;
  left: 0;
  z-index: 9999; /* 高z-index值 */
  width: 380px; /* 增加宽度，避免文字换行 */
  overflow: hidden; /* 确保内容不溢出 */
  pointer-events: auto; /* 确保可以点击 */
  background-color: v-bind('isDark ? "rgba(26, 26, 26, 0.85)" : "rgba(255, 255, 255, 0.85)"');
  backdrop-filter: blur(12px);
  border: 1px solid v-bind('isDark ? "#666666" : "#dcdfe6"'); /* 添加边框 */
  border-radius: 10px; /* 增大圆角 */
  box-shadow: 0 8px 32px 0 v-bind('isDark ? "rgb(0 0 0 / 80%)" : "rgb(0 0 0 / 25%)"'); /* 增强阴影效果 */
  animation: popup-fade-in 0.3s ease-out; /* 添加动画效果 */
}

@keyframes popup-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 18px;
  font-size: 15px;
  font-weight: 600;
  color: v-bind('isDark ? "#ffffff" : "#303133"');
  background: v-bind('isDark ? "rgba(51, 51, 51, 0.9)" : "rgba(245, 247, 250, 0.9)"');
  backdrop-filter: blur(8px);

  /* 毛玻璃效果 */
  border-bottom: 1px solid v-bind('isDark ? "#666666" : "#ebeef5"');
}
.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 按钮样式由Element Plus提供，这里只需要微调 */
.action-btn {
  padding: 0;
  margin: 0;
}
.popup-content {
  padding: 16px 18px;
  background-color: v-bind('isDark ? "rgba(26, 26, 26, 0.6)" : "rgba(255, 255, 255, 0.6)"');
  backdrop-filter: blur(5px);
}
.info-grid {
  display: grid;
  grid-gap: 6px;
  margin-bottom: 5px;
}
.info-item {
  display: flex;
  flex-wrap: nowrap; /* 确保不换行 */
  align-items: center; /* 居中对齐 */
  min-height: 22px; /* 确保每行有足够的高度 */
  padding-bottom: 5px;
  border-bottom: 1px solid v-bind('isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.03)"'); /* 添加分隔线 */
}
.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}
.label {
  width: 130px;
  font-size: 13px;
  font-weight: 500;
  color: v-bind('isDark ? "#e0e0e0" : "#606266"');
  letter-spacing: 0.3px;
}
.value {
  flex: 1;
  font-size: 13px;
  font-weight: 400;
  color: v-bind('isDark ? "#ffffff" : "#303133"');
  letter-spacing: 0.3px;
  word-break: break-all;
}

/* 功能按钮区域样式 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding-top: 12px;
  margin-top: 14px;
  border-top: 1px solid v-bind('isDark ? "rgba(255, 255, 255, 0.15)" : "rgba(0, 0, 0, 0.05)"');
}
.action-buttons .el-button {
  flex: 1;
  padding: 8px 15px;
  margin: 0 5px;
  font-size: 13px;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.action-buttons .el-button:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transform: translateY(-1px);
}
.action-buttons .el-button:first-child {
  margin-left: 0;
}
.action-buttons .el-button:last-child {
  margin-right: 0;
}
</style>
