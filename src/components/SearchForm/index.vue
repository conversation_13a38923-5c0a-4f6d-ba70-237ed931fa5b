<template>
  <div v-if="columns.length" class="card table-search">
    <el-form ref="formRef" :model="searchParam">
      <Grid ref="gridRef" :collapsed="collapsed" :gap="[20, 0]" :cols="searchCol">
        <GridItem v-for="(item, index) in columns" :key="item.prop" v-bind="getResponsive(item)" :index="index">
          <el-form-item>
            <template #label>
              <el-space :size="4">
                <span>{{ `${item.search?.label ?? item.label}` }}</span>
                <el-tooltip v-if="item.search?.tooltip" effect="dark" :content="item.search?.tooltip" placement="top">
                  <i :class="'iconfont icon-yiwen'"></i>
                </el-tooltip>
              </el-space>
              <span>&nbsp;:</span>
            </template>
            <SearchFormItem :column="item" :search-param="searchParam" />
          </el-form-item>
        </GridItem>
        <GridItem suffix>
          <div class="operation">
            <el-button type="primary" :icon="Search" @click="search">{{ $t("project.search") }}</el-button>
            <el-button :icon="Delete" @click="reset">{{ $t("project.reset") }}</el-button>
            <el-button v-if="showCollapse" type="primary" link class="search-isOpen" @click="collapsed = !collapsed">
              {{ collapsed ? $t("common.expand") : $t("common.collapse") }}
              <el-icon class="el-icon--right">
                <component :is="collapsed ? ArrowDown : ArrowUp"></component>
              </el-icon>
            </el-button>
          </div>
        </GridItem>
      </Grid>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="SearchForm">
import { ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import { Search, Delete, ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import Grid from "@/components/Grid/index.vue";
import GridItem from "@/components/Grid/components/GridItem.vue";
import SearchFormItem from "./components/SearchFormItem.vue";
import type { ColumnProps } from "@/components/ProTable/interface";

interface Props {
  columns: ColumnProps[];
  searchParam: { [key: string]: any };
  searchCol?: number | Record<string, number>;
  search: () => void;
  reset: () => void;
}

const props = defineProps<Props>();

const formRef = ref<FormInstance>();
const gridRef = ref();
const collapsed = ref(true);

const toSpanObj = (val, def) => {
  if (typeof val === "object") return val;
  if (typeof val === "number") return { span: val };
  return { span: def };
};

const getResponsive = (item: ColumnProps) => {
  const res: any = {};
  if (item.search?.span) res.span = item.search.span;
  if (item.search?.xs) res.xs = toSpanObj(item.search.xs, 1);
  if (item.search?.sm) res.sm = toSpanObj(item.search.sm, 2);
  if (item.search?.md) res.md = toSpanObj(item.search.md, 2);
  if (item.search?.lg) res.lg = toSpanObj(item.search.lg, 3);
  if (item.search?.xl) res.xl = toSpanObj(item.search.xl, 4);
  return res;
};

// 判断是否显示 展开/合并 按钮
const showCollapse = computed(() => {
  let show = false;
  props.columns.reduce((prev, current) => {
    prev +=
      (current.search![gridRef.value?.breakPoint]?.span ?? current.search?.span ?? 1) +
      (current.search![gridRef.value?.breakPoint]?.offset ?? current.search?.offset ?? 0);
    if (typeof props.searchCol !== "number") {
      if (prev >= props.searchCol[gridRef.value?.breakPoint]) show = true;
    } else {
      if (prev >= props.searchCol) show = true;
    }
    return prev;
  }, 0);
  return show;
});

defineExpose({
  formRef,
  gridRef,
  collapsed
});
</script>

<style scoped lang="scss">
.table-search {
  padding: 16px 24px 0;
  margin-bottom: 16px;
  background: rgb(255 255 255 / 70%);
  backdrop-filter: blur(10px);
  border: 1px solid rgb(255 255 255 / 18%);
  border-radius: 16px;
  box-shadow: 0 4px 16px 0 rgb(0 0 0 / 10%);
  transition: box-shadow 0.3s;
  .el-form-item {
    margin-bottom: 12px;
    .el-form-item__label {
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
    .el-input__wrapper,
    .el-select,
    .el-date-editor {
      border-radius: 8px;
      transition: box-shadow 0.3s;
      &:hover {
        box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
      }
      &.is-focus {
        box-shadow: 0 0 0 1.5px var(--el-color-primary) inset;
      }
    }
  }
  .operation {
    display: flex;
    gap: 15px 12px;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 10px;
    .el-button {
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s;
      &:hover {
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
        transform: translateY(-1px);
      }
    }
  }
}
</style>
