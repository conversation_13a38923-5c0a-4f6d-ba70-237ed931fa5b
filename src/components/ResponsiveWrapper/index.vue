<template>
  <div :class="wrapperClasses">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useResponsive } from "@/hooks/useResponsive";

interface Props {
  /** 是否启用移动端表格卡片化 */
  enableMobileCards?: boolean;
  /** 是否启用移动端表格滚动优化 */
  enableMobileScroll?: boolean;
  /** 是否启用移动端表格操作按钮优化 */
  enableMobileActions?: boolean;
  /** 是否启用移动端表格状态指示器优化 */
  enableMobileStatus?: boolean;
  /** 是否启用移动端表格搜索优化 */
  enableMobileSearch?: boolean;
  /** 是否启用移动端表格分页优化 */
  enableMobilePagination?: boolean;
  /** 自定义类名 */
  customClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  enableMobileCards: false,
  enableMobileScroll: true,
  enableMobileActions: true,
  enableMobileStatus: true,
  enableMobileSearch: true,
  enableMobilePagination: true,
  customClass: ""
});

const { getResponsiveClass, isMobile, isTablet } = useResponsive();

const wrapperClasses = computed(() => {
  const classes = [getResponsiveClass(), props.customClass];

  // 根据props添加相应的移动端优化类名
  if (props.enableMobileCards) {
    classes.push("mobile-table-cards");
  }

  if (props.enableMobileScroll) {
    classes.push("mobile-scroll-table");
  }

  if (props.enableMobileActions) {
    classes.push("mobile-table-actions");
  }

  if (props.enableMobileStatus) {
    classes.push("mobile-table-status");
  }

  if (props.enableMobileSearch) {
    classes.push("mobile-table-search");
  }

  if (props.enableMobilePagination) {
    classes.push("mobile-table-pagination");
  }

  return classes.filter(Boolean).join(" ");
});

// 暴露响应式状态供父组件使用
defineExpose({
  isMobile,
  isTablet,
  getResponsiveClass
});
</script>

<style scoped lang="scss">
.responsive-wrapper {
  width: 100%;
  height: 100%;
}
</style>
