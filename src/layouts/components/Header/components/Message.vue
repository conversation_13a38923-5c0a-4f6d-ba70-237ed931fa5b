<template>
  <div class="message">
    <el-popover placement="bottom" :width="360" trigger="click">
      <template #reference>
        <el-badge v-if="unreadMessages?.length > 0" :value="unreadMessages?.length || 0" class="item">
          <i :class="'iconfont icon-xiaoxi'" class="toolBar-icon"></i>
        </el-badge>
        <i v-else :class="'iconfont icon-xiaoxi'" class="toolBar-icon"></i>
      </template>
      <el-tabs v-model="activeName">
        <el-tab-pane :label="`${t('common.message')} (${unreadMessages?.length || 0})`" name="first">
          <div class="message-container">
            <el-scrollbar class="message-scrollbar" v-if="unreadMessages?.length > 0">
              <div class="message-list">
                <div class="message-item" v-for="item in unreadMessages" :key="item.id" @click="navigateToLogsMessages">
                  <img src="@/assets/images/msg01.png" alt="" class="message-icon" />
                  <div class="message-content">
                    <span class="message-title">{{ isChinese ? item.title : item.titleEn || item.title }}</span>
                    <div class="message-text">{{ isChinese ? item.message : item.messageEn || item.message }}</div>
                    <span class="message-date">{{ item.creationTime }}</span>
                  </div>
                </div>
              </div>
            </el-scrollbar>
            <div class="message-empty" v-else>
              <img src="@/assets/images/notData.png" alt="notData" />
              <div>{{ $t("common.noMessage") }}</div>
            </div>
            <div class="view-all" @click="navigateToLogsMessages">
              <span>{{ $t("common.viewAll") }}</span>
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { onMounted, ref, computed, watch } from "vue";
import { getUnreadMessages } from "@/api/modules/message";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import { ArrowRight } from "@element-plus/icons-vue";
const { t } = useI18n();
const router = useRouter();

const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");

interface Message {
  id: string;
  title: string;
  titleEn: string;
  message: string;
  messageEn: string;
  deviceType: string;
  markRead: number;
  creationTime: string;
}

const activeName = ref("first");
let unreadMessages = ref<Message[]>();

const getUnreadMessagesData = async () => {
  try {
    const resultData = await getUnreadMessages();
    if (resultData && resultData.code === "200") {
      // 确保 resultData.data 是一个数组，并且每个元素都符合 Message 接口
      if (Array.isArray(resultData.data)) {
        unreadMessages.value = resultData.data;
      } else {
        console.error("Invalid data format:", resultData.data);
      }
    }
  } catch (error) {
    console.error("Failed to fetch unread messages:", error);
  }
};

const navigateToLogsMessages = () => {
  router.push("/logs/messages"); // 跳转到 /logs/messages
};

onMounted(() => {
  getUnreadMessagesData();
});

// Watch for changes in messageCounter to refresh unread messages
watch(
  () => globalStore.messageCounter,
  () => {
    getUnreadMessagesData();
  }
);
</script>

<style scoped lang="scss">
.message-container {
  display: flex;
  flex-direction: column;
  height: 300px;
  overflow: hidden;
  background: #ffffff;
  border-radius: 12px;
}
.message-scrollbar {
  flex: 1 1 auto;
  min-height: 0;
}
.message-empty {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  line-height: 45px;
  img {
    width: 80px;
    margin-bottom: 10px;
  }
}
.message-list {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 0;
  padding: 12px;
  overflow-y: auto;
  .message-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    margin-bottom: 12px;
    cursor: pointer;
    background: #f6f8fa;
    border-bottom: none;
    border-radius: 10px;
    box-shadow: 0 1px 4px 0 rgb(0 0 0 / 4%);
    transition: all 0.3s ease;
    &:hover {
      background-color: #f0f4fa;
    }
    &:last-child {
      margin-bottom: 0;
    }
    .message-icon {
      width: 40px;
      height: 40px;
      margin: 0 15px 0 0;
      border-radius: 8px;
    }
    .message-content {
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
      .message-title {
        margin-bottom: 5px;
        overflow: hidden;
        font-weight: 500;
        color: var(--el-text-color-primary);
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .message-text {
        display: -webkit-box;
        margin-bottom: 5px;
        overflow: hidden;
        font-size: 13px;
        line-height: 1.5;
        color: var(--el-text-color-regular);
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .message-date {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
.view-all {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  font-size: 13px;
  font-weight: 500;
  color: var(--el-color-primary);
  cursor: pointer;
  background-color: var(--el-fill-color-lighter);
  border-top: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  &:hover {
    color: var(--el-color-primary-dark-2);
    background-color: var(--el-fill-color-light);
  }
  .el-icon {
    margin-left: 5px;
    font-size: 12px;
    transition: transform 0.3s ease;
  }
  &:hover .el-icon {
    transform: translateX(3px);
  }
}
</style>

<style>
/* 全局覆盖消息弹窗样式 - 最高优先级 */
.el-popper.is-light[data-popper-placement^="bottom"] {
  overflow: hidden !important;
  background: rgb(255 255 255 / 95%) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgb(255 255 255 / 18%) !important;
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgb(0 0 0 / 10%) !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}
.el-popper.is-light[data-popper-placement^="bottom"]:hover {
  background: rgb(255 255 255 / 100%) !important;
  box-shadow: 0 12px 32px rgb(0 0 0 / 15%) !important;
  transform: translateY(-5px) !important;
}
.el-popper.is-light .el-popover__content {
  padding: 0 !important;
  overflow: hidden !important;
  background: transparent !important;
  border: none !important;
  border-radius: 16px !important;
  box-shadow: none !important;
}
</style>
