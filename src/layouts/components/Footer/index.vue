<template>
  <div class="footer">
    <div class="footer-content">
      <div class="footer-main">
        <div class="copyright">
          {{ $t("footer.copyright") }}
        </div>
        <div class="beian-info">
          <a
            class="beian-link"
            href="https://beian.miit.gov.cn"
            target="_blank"
            rel="noopener noreferrer"
            :title="$t('footer.beianLink')"
          >
            <i class="iconfont icon-beian"></i>
            {{ $t("footer.beianNumber") }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页尾组件不需要引入useI18n，因为模板中直接使用了$t()
</script>

<style scoped lang="scss">
.footer {
  min-height: 60px;
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-bg-color-page) 100%);
  border-top: 1px solid var(--el-border-color-light);
  box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
  transition: all 0.3s ease;
  .footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 1400px;
    min-height: 44px;
    margin: 0 auto;
  }
  .footer-main {
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    text-align: center;
  }
  .copyright {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    color: var(--el-text-color-primary);
    letter-spacing: 0.3px;
    transition: color 0.3s ease;
    &:hover {
      color: var(--el-color-primary);
    }
  }
  .beian-info {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .beian-link {
    display: flex;
    gap: 4px;
    align-items: center;
    font-size: 12px;
    color: var(--el-text-color-secondary);
    text-decoration: none;
    letter-spacing: 0.2px;
    opacity: 0.8;
    transition: all 0.3s ease;
    .iconfont {
      font-size: 12px;
      opacity: 0.7;
    }
    &:hover {
      color: var(--el-color-primary);
      opacity: 1;
      transform: translateY(-1px);
      .iconfont {
        opacity: 1;
        transform: scale(1.05);
      }
    }
  }

  // 响应式设计
  @media screen and (width <= 768px) {
    position: relative; // 确保在移动端正确定位
    z-index: 1; // 防止被其他元素遮挡
    min-height: 50px;
    padding: 12px 16px;
    .footer-content {
      min-height: 40px;
    }
    .footer-main {
      gap: 4px;
    }
    .copyright {
      font-size: 13px;
    }
    .beian-link {
      font-size: 11px;
      .iconfont {
        font-size: 11px;
      }
    }
  }

  @media screen and (width <= 480px) {
    min-height: 45px;
    padding: 10px 12px;
    margin-bottom: env(safe-area-inset-bottom, 0); // 适配iOS安全区域
    .footer-content {
      min-height: 35px;
    }
    .copyright {
      font-size: 12px;
      line-height: 1.3;
    }
    .beian-link {
      gap: 3px;
      font-size: 10px;
      .iconfont {
        font-size: 10px;
      }
    }
  }

  // 暗色主题适配
  .dark & {
    background: linear-gradient(135deg, var(--el-bg-color) 0%, rgb(0 0 0 / 10%) 100%);
    border-top-color: var(--el-border-color);
    box-shadow: 0 -2px 8px rgb(0 0 0 / 15%);
  }
}
</style>
