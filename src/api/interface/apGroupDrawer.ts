import { useUserStore } from "@/stores/modules/user";
import { deviceStatusEnum, getDeviceConfigJwe, pushDeviceConfigJwe, renameDevice } from "@/api/modules/project";
import { computed, reactive, ref, watch } from "vue";
import { Configuration } from "@/api/interface/configuration";
import type { Project } from "@/api/interface/project";
import { removeEmptyValues } from "@/utils";

import { useI18n } from "vue-i18n";
import i18n from "@/languages";
import { ElMessage } from "element-plus";
import { deviceTrafficWeeklyReport } from "@/api/modules/deviceConfigDrawer";
import {
  deviceConfig,
  DeviceConfigData,
  DeviceWeekStatistics,
  drawerProps,
  SpeedDuplex,
  swIsolate,
  swPoe,
  swPort,
  swQos,
  swStorm,
  swVlan
} from "@/api/interface/device/model";
import { getDescription } from "@/api/interface/device/formatter";
import { cloneDeep } from "lodash";

const t = i18n.global.t;

/**
 * 设备数据加载命令类型
 */
export enum DeviceDataCmd {
  CONFIG = 10, // 设备配置
  STATUS = 4 // 设备状态
}

/**
 * 设备数据类型
 */
export enum DeviceDataType {
  CONFIG = "设备配置",
  STATUS = "设备状态"
}

export const apGroupList = ref<Project.ApGroup[]>([
  {
    group_id: 0,
    alias: t("device.group1")
  },
  {
    group_id: 1,
    alias: t("device.group2")
  },
  {
    group_id: 2,
    alias: t("device.group3")
  },
  {
    group_id: 3,
    alias: t("device.group4")
  }
]);

/**
 * 加载设备数据的参数接口
 */
interface LoadDeviceDataParams {
  cmd: DeviceDataCmd;
  deviceId: string;
  userId: string;
  data: DeviceConfigData;
}

/**
 * 加载设备数据
 * @param cmd 命令类型
 * @param target 目标对象
 * @param type 数据类型
 * @returns Promise<void>
 */
const loadDeviceData = async (
  cmd: DeviceDataCmd,
  target: Partial<Configuration.DeviceConfig>,
  type: DeviceDataType
): Promise<void> => {
  try {
    // 构建请求参数
    const params: LoadDeviceDataParams = {
      cmd,
      deviceId: drawerProps.value.row.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {}
    };

    // 处理 supports 数据
    const supports = drawerProps.value.row.supports;
    if (supports) {
      const supportTypes = ["wireless", "network", "system"] as const;
      supportTypes.forEach(supportType => {
        if (supports[supportType]) {
          params.data[supportType] = supports[supportType].supports;
        }
      });
    }

    console.log(`正在加载${type}数据，参数:`, params);
    const response = await getDeviceConfigJwe(params);

    if (!response?.data) {
      throw new Error(`获取${type}数据失败: 响应数据为空`);
    }

    // 过滤并合并数据
    const filteredData = filterEmptyData(response.data);
    Object.assign(target, filteredData);

    // 特殊处理设备状态的 bootTime
    if (type === DeviceDataType.STATUS && response.bootTime) {
      drawerProps.value.row.bootTime = Number(response.bootTime);
    }

    console.log(`${type}数据加载成功:`, filteredData);
  } catch (error) {
    console.error(`${type}数据加载失败:`, error);
    ElMessage.error(`${type}加载失败: ${error instanceof Error ? error.message : "未知错误"}`);
    throw error;
  }
};

/**
 * 加载设备配置
 * @returns Promise<void>
 */
export const loadDeviceConfig = async (): Promise<void> => {
  await loadDeviceData(DeviceDataCmd.CONFIG, deviceConfig, DeviceDataType.CONFIG);
};

/**
 * 加载设备状态
 * @returns Promise<void>
 */
export const loadDeviceStatus = async (): Promise<void> => {
  await loadDeviceData(DeviceDataCmd.STATUS, deviceStatus, DeviceDataType.STATUS);
};

// 过滤掉未返回的数据
function filterEmptyData(data) {
  // 使用全局的removeEmptyValues函数处理数据
  return removeEmptyValues(data);
}

export const deviceStatus = reactive<Partial<Configuration.DeviceConfig>>({
  wireless: {
    wifiTime: {
      enabled: 0,
      week: "",
      beginTime: "",
      endTime: ""
    },
    radio0: {
      hidden: 0,
      chanList: [],
      txpower: 0,
      channel: 0,
      disabled: 0,
      ssid: "",
      key: ""
    },
    radio1: {
      hidden: 0,
      chanList: [],
      txpower: 0,
      channel: 0,
      disabled: 0,
      ssid: "",
      key: ""
    },
    guest: {
      rate: 0,
      wifiTime: 0,
      disabled: 0,
      ssid: "",
      key: "",
      hidden: 0
    }
  },
  network: {
    lan: {
      netmask: "",
      ipaddr: ""
    },
    dhcp: {
      dnsenabled: 0,
      dns1: "",
      dns2: "",
      start: 0,
      limit: 0
    },
    wan: [],
    wanMax: 0,
    workmode: "",
    brAp: {
      ssid: "",
      key: "",
      hidden: 0,
      mode: 0,
      channel: 0,
      txpower: 0,
      chanList: []
    },
    join: {
      ssid: "",
      key: "",
      radio: 0
    },
    brSafe: {
      mode: 0,
      status: "",
      timedown: 0,
      time: 0
    },
    swRstp: [],
    swLldp: [],
    swVlan: [],
    swIsolate: []
  },
  system: {
    reboot: {
      week: "",
      rateDelay: 0,
      time: "",
      enabled: 0
    },
    led: {
      mode: "",
      beginTime: "",
      endTime: ""
    },
    userList: [],
    sysPassword: "",
    reSysPassword: "",
    swPort: [],
    swPoe: [],
    swStorm: [],
    swQos: [],
    swVlan: [],
    sysSave: 0
  }
});

// 控制加密状态的变量
export let encryptRadio0 = ref<boolean>(false);
export let encryptRadio1 = ref<boolean>(false);
export let encryptGuest = ref<boolean>(false);

// 计算属性来决定加密状态
export const encryptionRadio0Method = computed({
  get() {
    return encryptRadio0.value;
  },
  set(value: boolean) {
    encryptRadio0.value = value;
  }
});

export const encryptionRadio1Method = computed({
  get() {
    return encryptRadio1.value;
  },
  set(value: boolean) {
    encryptRadio1.value = value;
  }
});

export const encryptionGuestMethod = computed({
  get() {
    return encryptGuest.value;
  },
  set(value) {
    encryptGuest.value = value;
  }
});

// 初始化时，根据 key 的值设置加密状态
watch(
  () => deviceConfig.wireless.radio0.key,
  newValue => {
    encryptRadio0.value = !!newValue; // 如果 key 不为空，表示加密状态
  },
  { immediate: true } // 确保在组件挂载时执行一次
);

// 初始化时，根据 key 的值设置加密状态
watch(
  () => deviceConfig.wireless.radio1.key,
  newValue => {
    encryptRadio1.value = !!newValue; // 如果 key 不为空，表示加密状态
  },
  { immediate: true } // 确保在组件挂载时执行一次
);

// 初始化时，根据 key 的值设置加密状态
watch(
  () => deviceConfig.wireless.guest.key,
  newValue => {
    encryptGuest.value = !!newValue; // 如果 key 不为空，表示加密状态
  },
  { immediate: true } // 确保在组件挂载时执行一次
);

export const startIpPrefix = computed(() => {
  // 获取 LAN 的 IP 地址前三部分
  const lanIp = deviceConfig.network.lan.ipaddr || "";
  const parts = lanIp.split(".");

  // 拼接前三部分 IP 地址
  return parts.length === 4 ? parts.slice(0, 3).join(".") + "." : "";
});

// 监听 row 数据的变化，确保在数据更新后处理
watch(
  () => drawerProps.value.row,
  newRow => {
    if (newRow.supports) {
      // 如果 supports 是 JSON 字符串，则进行解析
      if (typeof newRow.supports === "string") {
        newRow.supports = JSON.parse(newRow.supports);
      }
      console.log("解析后的 supports 数据:", JSON.stringify(newRow.supports));
    }
  },
  { immediate: true }
); // `immediate: true` 确保在组件挂载时立即执行

export const handleStartIpChange = (value: string) => {
  // 限制用户输入为数字，并且仅修改最后一部分
  const numValue = parseInt(value.replace(/[^0-9]/g, ""), 10);
  deviceConfig.network.dhcp.start = isNaN(numValue) ? 0 : numValue;
};

const status = computed(() => drawerProps.value.row.status);

export const statusLabel = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => Number(item.value) === Number(status.value));
  return statusItem ? statusItem.label : "";
});

export const statusTagType = computed(() => {
  const statusItem = deviceStatusEnum.value.find(item => Number(item.value) === Number(status.value));
  return statusItem ? statusItem.tagType : "";
});

// 使用 ref 存储 Date 类型的时间，添加默认值处理
const defaultBeginTime = new Date("1970-01-01T00:00:00");
const defaultEndTime = new Date("1970-01-01T23:59:59");

export const beginTime = ref<Date>(
  deviceConfig.wireless?.wifiTime?.beginTime
    ? new Date(`1970-01-01T${deviceConfig.wireless.wifiTime.beginTime}:00`)
    : defaultBeginTime
);

export const endTime = ref<Date>(
  deviceConfig.wireless?.wifiTime?.endTime ? new Date(`1970-01-01T${deviceConfig.wireless.wifiTime.endTime}:00`) : defaultEndTime
);

// 监听 beginTime 和 endTime 的变化
watch(beginTime, newVal => {
  deviceConfig.wireless.wifiTime.beginTime = newVal.toTimeString().slice(0, 5);
});
watch(endTime, newVal => {
  deviceConfig.wireless.wifiTime.endTime = newVal.toTimeString().slice(0, 5);
});

// 处理时间输入，转换为字符串
export const handleTimeInput = (key: string) => {
  if (!deviceConfig.wireless?.wifiTime) {
    deviceConfig.wireless = {
      ...deviceConfig.wireless,
      wifiTime: {
        week: "",
        beginTime: "",
        endTime: "",
        enabled: 0
      }
    };
  }

  const time = deviceConfig.wireless.wifiTime[key];
  if (time instanceof Date) {
    // 将时间转换为 "HH:mm" 格式的字符串
    deviceConfig.wireless.wifiTime[key] = time.toTimeString().slice(0, 5);
  }
};

export const formatAutoneg = (autoneg: number) => {
  const { t } = useI18n(); // 此方法需要在 Vue 的上下文中调用
  if (autoneg === 0) {
    return t("common.enforcementMode") + "/" + t("common.disconnect");
  } else {
    return t("common.adaptiveMode") + "/" + t("common.disconnect");
  }
};

export const getPortStates = () => {
  const { t } = useI18n();

  return [
    { icon: "/src/assets/images/port_active_icon.png", text: `${t("common.connected")} | ${t("common.powerOff")}` },
    { icon: "/src/assets/images/port_deactive_icon.png", text: `${t("common.disconnected")} | ${t("common.powerOff")}` },
    { icon: "/src/assets/images/port_active_ele_icon.png", text: `${t("common.connected")} | ${t("common.powerOn")}` },
    { icon: "/src/assets/images/port_deactive_ele_icon.png", text: `${t("common.disconnected")} | ${t("common.powerOn")}` }
  ];
};

export let portDialogVisible = ref(false);

export const showPortDialog = () => {
  portDialogVisible.value = true;
  console.log("showPortDialog called:", portDialogVisible.value); // 调试输出
};

export const selectedRows = ref<any[]>([]); // 保存选中的行数据

// 处理选中行的变化
export const handleSelectionChange = (selection: any[]) => {
  console.log("handleSelectionChange called:", selection);
  if (selection.length > 0) {
    console.log("selection:", selection);
    swPort.value = selection[0];
    const selectedPort = selection[0].name;
    // 查找 deviceConfig.system.swPoe 数组中 port 字段与选中项的 port 匹配的对象
    if (deviceConfig.system.swPoe) {
      const matchingPoe = deviceConfig.system.swPoe.find(poe => poe.name === selectedPort);
      if (matchingPoe) {
        swPoe.value = matchingPoe;
        console.log("swPoe:", JSON.stringify(swPoe.value));
      }
    }
    if (deviceConfig.network.swVlan) {
      const matchingVlan = deviceConfig.network.swVlan.find(vlan => vlan.name === selectedPort);
      if (matchingVlan) {
        swVlan.value = matchingVlan;
        console.log("swVlan:", swVlan.value);
      }
    }
    if (deviceConfig.system.swStorm) {
      const matchingStorm = deviceConfig.system.swStorm.find(storm => storm.name === selectedPort);
      if (matchingStorm) {
        swStorm.value = matchingStorm;
        console.log("swStorm:", JSON.stringify(swStorm.value));
      }
    }
    if (deviceConfig.network.swIsolate) {
      const matchingSwIsolate = deviceConfig.network.swIsolate.find(vlan => vlan.name === selectedPort);
      if (matchingSwIsolate) {
        swIsolate.value = matchingSwIsolate;
        console.log("swIsolate:", JSON.stringify(swIsolate.value));
      }
    }
  }
  selectedRows.value = selection;
};

export const showPortExample = ref(false);
// 切换图示显示状态
export const togglePortExample = () => {
  console.log("selectedRows:{}", selectedRows);
  showPortExample.value = !showPortExample.value;
};

// 计算属性提取 name 字段并用逗号连接
export const selectedRowNames = computed(() => {
  return selectedRows.value.map(row => row.describe || row.name).join(", ");
});

// 判断行是否被选中
export const isRowSelected = row => {
  return selectedRows.value.some(selected => selected.name === row.name);
};

export const isPortSelected = row => {
  if (isolateRows.value.length === 0 && swIsolate.value && swIsolate.value.isolate && swIsolate.value.isolate.length > 0) {
    console.log("swIsolate.value:", swIsolate.value);
    return swIsolate.value.isolate.includes(row.port);
  }
  return isolateRows.value.some(selected => selected.name === row.name);
};

export const portTableRef = ref(null);

export const toggleRowSelection = item => {
  console.log("toggleRowSelection called:", item);
  const index = selectedRows.value.findIndex(row => row.name === item.name);
  if (index === -1) {
    // 未选中，则添加到选中数组
    selectedRows.value.push(item);
  } else {
    // 已选中，则从选中数组中移除
    selectedRows.value.splice(index, 1);
  }
  // 手动更新 el-table 的选中项
  if (portTableRef.value) {
    portTableRef.value.toggleRowSelection(item); // 手动切换表格的选中状态
  }
};

// 监听关闭事件
export const onDrawerClose = () => {
  selectedRows.value = []; // 清空选中项
  deviceNameChanged.value = false; // 重置变量
  editName.value = false;
  activeName.value = "first";
};

// 将枚举转换为选项数组
export const speedDuplexOptions = Object.entries(SpeedDuplex)
  .filter(([, value]) => !isNaN(Number(value))) // 只保留数值类型的项
  .map(([, value]) => ({
    label: Number(value), // 使用国际化函数生成 label
    value: Number(value) // 枚举值作为选项值
  }));

export enum PoePower {
  AF = 0, // af (15.4w)
  AT = 1 // at (30w)
}

// 构建选项列表
export const poePowerOptions = {
  [PoePower.AF]: "af (15.4w)",
  [PoePower.AT]: "at (30w)"
};

export enum VlanMode {
  ACCESS = 0, // access
  TRUNK = 1, // trunk
  HYBRID = 2 // hybrid
}

// 构建 VLAN 模式选项
export const vlanModeOptions = {
  [VlanMode.ACCESS]: "access",
  [VlanMode.TRUNK]: "trunk",
  [VlanMode.HYBRID]: "hybrid"
};

export enum trafficType {
  UNICAST = 4, // unicast
  MULTICAST = 1, // multicast
  BROADCAST = 2 // broadcast
}

// 枚举转为数组
export const trafficTypeOptionsArray = Object.keys(trafficType)
  .filter(key => isNaN(Number(key))) // 过滤掉数字的部分
  .map(key => ({
    label: key, // 选项的显示文本
    value: trafficType[key as keyof typeof trafficType], // 对应的值
    description: getDescription(key) // 添加中文描述
  }));

// 为每个枚举值定义中文描述

// 更新 trafficType 的逻辑
export const selectedTrafficTypes = computed({
  get: () => {
    console.log("Current trafficType:", swStorm.value);
    // 通过位运算解析当前选中的类型
    return trafficTypeOptionsArray.filter(option => (swStorm.value.trafficType & option.value) !== 0).map(option => option.value);
  },
  set: newValues => {
    let updatedValue = 0;
    console.log("New selected values:", newValues);
    // 合并选中的值
    newValues.forEach(value => {
      updatedValue |= value;
      console.log("Current updatedValue:", updatedValue);
    });

    console.log("Before setting trafficType:", swStorm.value.trafficType);
    swStorm.value.trafficType = updatedValue;
    console.log("After setting trafficType:", swStorm.value.trafficType);
  }
});

// 监控 selectedTrafficTypes 的变化
watch(
  () => swStorm.value.trafficType,
  newVal => {
    console.log("swPoe trafficType changed:", newVal);
  }
);

// 当复选框变化时，更新 trafficType 的值
export const updateTrafficType = () => {
  console.log("Updated trafficType:", swStorm.value.trafficType);
};

// 预加载常用值函数
export const preloadRateOptions = async () => {
  const commonOptions = {
    label: t("device.commonRate"),
    value: "common",
    children: [
      { label: "64", value: 64, isLeaf: true },
      { label: "128", value: 128, isLeaf: true },
      { label: "256", value: 256, isLeaf: true },
      { label: "512", value: 512, isLeaf: true },
      { label: "1024", value: 1024, isLeaf: true },
      { label: "2048", value: 2048, isLeaf: true },
      { label: "4096", value: 4096, isLeaf: true },
      { label: "8192", value: 8192, isLeaf: true },
      { label: "16384", value: 16384, isLeaf: true },
      { label: "32768", value: 32768, isLeaf: true }
    ],
    isLeaf: false,
    disabled: true,
    hasChildren: true
  };

  const customOptions = {
    label: t("device.more"),
    value: "custom",
    children: null,
    isLeaf: false,
    loading: false,
    disabled: false,
    expanded: false,
    loaded: false,
    hasChildren: true,
    lazy: true
  };

  return [commonOptions, customOptions];
};

export let rateOptions = ref<Array<any>>([]);

export const isolateRows = ref<any[]>([]); // 保存选中的行数据
export const togglePortSelection = item => {
  console.log("togglePortSelection called:", item);
  const index = isolateRows.value.findIndex(row => row.name === item.name);
  if (index === -1) {
    // 未选中，则添加到选中数组
    isolateRows.value.push(item);
  } else {
    // 已选中，则从选中数组中移除
    isolateRows.value.splice(index, 1);
  }
  console.log("isolateRows updated:", isolateRows.value);
};

export const isolateAll = () => {
  // 获取所有端口项
  const allPorts = deviceConfig.system.swPort;

  if (isolateRows.value.length === allPorts.length) {
    // 如果所有端口已经被选中，则清空选中的端口
    isolateRows.value = [];
  } else {
    // 否则，选中所有端口
    isolateRows.value = [...allPorts];
  }

  console.log("isolateRows updated:", isolateRows.value);
};

export const getPortNames = ports => {
  if (!ports?.isolate) {
    return []; // 如果 ports 或 ports.isolate 不存在，返回空数组
  }
  // console.log("getPortNames called:", ports.isolate);
  return ports?.isolate
    .map(portId => {
      const port = deviceConfig.system.swPort.find(p => p.port === portId);
      return port ? port.name : ""; // 返回 name 字段
    })
    .filter(name => name !== ""); // 过滤掉空字符串
};

export const editName = ref(false);

export const deviceNameChanged = ref(false);

export const editDeviceName = (row: any) => {
  console.log("editDeviceName called. row:", row);
  editName.value = !editName.value;
};

export const saveDeviceName = async (row: any) => {
  console.log("editDeviceName called. row:", JSON.stringify(row));
  editName.value = !editName.value;
  if (!row.deviceName) {
    return;
  }
  const params = {
    deviceName: row.deviceName,
    deviceId: drawerProps.value.row.deviceId
  };
  try {
    const response = await renameDevice(params);
    if (response && response.code === "200") {
      console.log("Device renamed successfully, response:", response);
      // 请求成功的提示消息
      ElMessage({
        message: response.msg || t("common.operationSuccess"), // 使用 response.msg
        type: "success", // 成功类型
        duration: 3000
      });
    } else {
      // 请求失败的提示消息
      ElMessage({
        message: response?.msg, // 使用 response.msg
        type: "error", // 失败类型
        duration: 3000
      });
    }
  } catch (error) {
    console.error("Failed to rename device:", error);
    // 请求异常的提示消息
    ElMessage({
      message: "设备名称更新时发生异常，请检查网络或稍后重试！",
      type: "error", // 异常类型
      duration: 3000
    });
  }
};

export const handleReboot = async () => {
  const t = i18n.global.t;
  try {
    const params: Project.ReqConfigParams = {
      cmd: 6,
      deviceId: drawerProps.value.row.deviceId,
      userId: useUserStore().userInfo.userId,
      data: {
        system: {
          sysReboot: 1
        }
      }
    };
    const response = await pushDeviceConfigJwe(params);
    if (response && response.code === "200" && response.msg === "success") {
      console.log("Device rebooted successfully, response:", response);
      // 请求成功的提示消息
      ElMessage({
        message: t("common.operationSuccess"), // 使用 response.msg
        type: "success", // 成功类型
        duration: 3000
      });
    } else {
      // 请求失败的提示
    }
  } catch (error) {
    console.error("Failed to reboot device:", error);
  }
};

export const progressShow = ref(false); // 控制进度条的显示状态
export const downloadingPer = ref(0); // 下载进度百分比
// 通用的消息提示函数
const showMessage = (message: string, type: "success" | "error") => {
  ElMessage({
    message,
    type,
    duration: 3000
  });
};

// 计算下载进度并显示
const handleDownloadProgress = (otaData: any, t: Function) => {
  const totalSize = otaData?.size || 1; // 避免除以 0
  const downloadedSize = otaData?.dlSize || 0;
  const per = Math.min(Math.round((downloadedSize / totalSize) * 100), 100);
  progressShow.value = true; // 显示进度条
  downloadingPer.value = per; // 更新进度百分比
  showMessage(t("common.downloadingUpdate", { percent: per }), "success");
};

// 处理升级状态
const handleUpgradeState = async (otaData: any, t: Function, fetchUpgradeStatus: Function) => {
  const upgradeState = otaData?.status;

  switch (upgradeState) {
    case "NONE":
      showMessage(t("common.discoverNewVer"), "success"); // 有新版本
      break;
    case "FAIL":
      showMessage(t("common.upgradeFailed"), "error"); // 更新失败
      break;
    case "DL":
      handleDownloadProgress(otaData, t);
      // 每 5 秒重新获取状态
      setTimeout(fetchUpgradeStatus, 5000);
      break;
    case "UPG":
      showMessage(t("common.upgrading"), "success"); // 正在升级
      break;
  }
};

// 获取设备升级状态
const fetchUpgradeStatus = async (deviceId: string, userId: string, t: Function) => {
  try {
    const configResponse = await getDeviceConfigJwe({
      cmd: 4,
      deviceId,
      userId,
      data: { system: ["ota"] }
    });

    if (configResponse?.code === "200" && configResponse.msg === "success") {
      const otaData = configResponse.data?.system?.ota;
      const upgradeStatus = otaData?.upgrade;

      if (upgradeStatus === 1) {
        await handleUpgradeState(otaData, t, () => fetchUpgradeStatus(deviceId, userId, t));
      } else {
        showMessage(t("common.noUpdates"), "success"); // 没有更新
      }
    } else {
      showMessage(configResponse?.msg || t("common.fetchingStatusError"), "error");
    }
  } catch (error) {
    console.error("Failed to fetch upgrade status:", error);
    showMessage(t("common.fetchingStatusError"), "error");
  }
};

// 主函数：触发升级
export const handleUpgrade = async () => {
  const t = i18n.global.t;
  const deviceId = drawerProps.value.row.deviceId;
  const userId = useUserStore().userInfo.userId;

  try {
    // 发送升级请求
    const upgradeResponse = await pushDeviceConfigJwe({
      cmd: 6,
      deviceId,
      userId,
      data: { system: { ota: { check: 1 } } }
    });

    if (upgradeResponse?.code === "200" && upgradeResponse.msg === "success") {
      console.log("Device upgrade check initiated successfully:", upgradeResponse);

      // 开始检查设备升级状态
      await fetchUpgradeStatus(deviceId, userId, t);
    } else {
      showMessage(upgradeResponse?.msg || t("common.upgradeFailed"), "error");
    }
  } catch (error) {
    console.error("Failed to upgrade device:", error);
    showMessage(t("common.upgradeFailed"), "error");
  }
};

export const deviceWeekStatistic = ref<
  | DeviceWeekStatistics
  | {
      deviceId: string;
      deviceName: string;
      beginDate: string;
      beginDateTimeZone?: unknown;
      endDate: string;
      endDateTimeZone?: unknown;
      bootTime: number;
      deviceType: string;
      deviceModel: string;
      rxByte: number;
      txByte: number;
      statistics: { date: string; rxByte: number; txByte: number; hour?: null }[];
    }
  | null
>(null);

export const getDeviceStatistics = async (params: { deviceId: string }) => {
  try {
    const response = await deviceTrafficWeeklyReport(params);
    if (response && response.code === "200" && response.msg === "success") {
      console.log("Device statistics retrieved successfully:", response);
      if (response.data) {
        deviceWeekStatistic.value = response.data || {}; // 更新统计数据
      }
    } else {
      deviceWeekStatistic.value = null; // 清除统计数据或设置为默认值
      console.error("Failed to retrieve device statistics:", response);
      throw new Error(response?.msg || "Failed to fetch device statistics");
    }
  } catch (error) {
    console.error("Error in getDeviceStatistics:", error);
    throw error;
  }
};
export const drawerVisible = ref(false);
const activeName = ref<"first" | "second" | "third" | "fourth" | "fifth">("first"); // 底层tabs默认选中第一个

// 根据选中的端口名称和端口号生成端口提交数据对象
export const generatePortData = (deviceConfig: any): any[] => {
  console.log("generatePortData called with deviceConfig:", deviceConfig);
  const portConfigs: any[] = [];
  selectedRows.value.forEach(port => {
    const config = cloneDeep(deviceConfig); // 使用深拷贝
    config.name = port.name;
    config.port = port.port;
    portConfigs.push(config);
  });
  return portConfigs;
};

export const closeDialog = () => {
  drawerVisible.value = false;
  activeName.value = "first";
};

export {
  deviceConfig,
  deviceTrafficWeeklyReport,
  drawerProps,
  swIsolate,
  showMessage,
  swPoe,
  swPort,
  swVlan,
  swQos,
  swStorm,
  activeName
};
