import http from "@/api";
import { PORT1 } from "@/api/config/servicePort";
import { Tools } from "@/api/interface";
import i18n from "@/languages/index";

export const getIpInfo = (ip: string, config?: any) => {
  return http.get(PORT1 + `/tools/getIpInfo/V2/${ip}`, {}, config);
};

export const getMacInfo = (mac: string) => {
  return http.get<Tools.MacInfoResponse>(PORT1 + `/oui/queryOui?mac=${mac}`);
};

export const pingTest = (ip: string, count: number) => {
  return http.get(PORT1 + `/tools/v2/ping?host=${ip}&count=${count}`, {}, { loading: false });
};

// 端口扫描请求
// export const portScan = async (ip: string, portStart: number, portEnd: number) => {
//   const response = await http.get(PORT1 + `/tools/scanPort?ip=${ip}&portStart=${portStart}&portEnd=${portEnd}`);
//
//   console.log("原始响应数据：", response.data); // 打印看看后端返回什么格式
//
//   return response.data?.data || []; // 确保返回的是数组
// };

// 端口扫描请求（仅负责发起请求，返回响应流的 Reader）
export const portScan = async (
  ip: string,
  portStart: number,
  portEnd: number
): Promise<ReadableStreamDefaultReader<Uint8Array>> => {
  // 使用绝对 URL 指定 http 协议，确保代理生效
  const url = PORT1 + `/api/tools/scanPort?ip=${ip}&portStart=${portStart}&portEnd=${portEnd}`;

  // 获取当前语言
  const language = i18n.global.locale.value === "en" ? "en_US" : "zh_CN";

  // 添加请求头
  const response = await fetch(url, {
    headers: {
      "Accept-Language": language
    }
  });

  if (!response.ok) {
    throw new Error(`请求失败，状态码: ${response.status}`);
  }
  // 返回响应体的读取器
  return response.body!.getReader();
};

export const calculateHeightOfBridge = (parms: {
  frequencyBand: number;
  distance: number;
  duration1: number;
  obstacleHeight: number;
}) => {
  return http.get(PORT1 + "/tools/calculateHeightOfBridge", parms);
};
