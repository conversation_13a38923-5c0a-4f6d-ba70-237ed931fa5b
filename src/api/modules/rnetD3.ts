import { queryRnetDevice, getDeviceById } from "./rnet";
import { getRnetDeviceStatusJwe } from "@/api/interface/rnet";
// import { ElLoading } from "element-plus";

// 全局存储设备对等连接数据
export const globalPeers: Record<string, { rxByte: number; txByte: number; time?: number; status?: string }> = {};

// 判断是否为数字
export const isNumber = (val: unknown): val is number => {
  return typeof val === "number" && !isNaN(val);
};

// 获取远程网络设备 - D3版本
export const getRnetDevice = async (id: string, devices: any[], showLoading = true) => {
  if (!id) return null;

  try {
    const res = await queryRnetDevice({ id }, { loading: showLoading });
    if (res.code !== "200" || !Array.isArray(res.data)) {
      console.error("Failed to fetch valid data");
      return null;
    }

    // 创建历史缓存数据的映射
    const cacheDataMap = new Map();
    res.data.forEach(item => {
      cacheDataMap.set(item.deviceId, item);
    });

    devices.length = 0;
    // 等待所有设备异步获取完毕
    await Promise.all(
      res.data.map(async item => {
        let deviceId = item.deviceId;
        try {
          const deviceRes = await getDeviceById({ deviceId });
          if (deviceRes && deviceRes.code === "200") {
            // 在线设备：获取完整状态信息
            if (deviceRes.data.status === 0) {
              const statusRes = await getRnetDeviceStatusJwe({ deviceId });
              if (statusRes && statusRes.code === "200") {
                let deviceObj = {
                  deviceId,
                  deviceName: deviceRes.data.deviceName || "",
                  deviceType: deviceRes.data.deviceType,
                  status: deviceRes.data.status,
                  data: statusRes.data,
                  supports: deviceRes.data.supports || [],
                  rate: "0 B/s"
                };
                devices.push(deviceObj);
              }
            } else {
              // 离线设备：使用历史缓存数据构建状态信息
              const cacheData = cacheDataMap.get(deviceId);
              const rNetData = cacheData
                ? {
                    stun: cacheData.stun,
                    wan: cacheData.wan,
                    map: cacheData.mappingIp,
                    allowedIPs: cacheData.allowedIps?.join(", ") || "",
                    peer: cacheData.conn || []
                  }
                : {};

              let deviceObj = {
                deviceId,
                deviceName: deviceRes.data.deviceName || "",
                deviceType: deviceRes.data.deviceType,
                status: deviceRes.data.status,
                data: {
                  system: {
                    rNet: rNetData
                  }
                },
                supports: deviceRes.data.supports || [],
                rate: "-"
              };
              devices.push(deviceObj);
            }
          } else {
            // 设备信息获取失败，但可能仍有缓存数据
            const cacheData = cacheDataMap.get(deviceId);
            const rNetData = cacheData
              ? {
                  stun: cacheData.stun,
                  wan: cacheData.wan,
                  map: cacheData.mappingIp,
                  allowedIPs: cacheData.allowedIps?.join(", ") || "",
                  peer: cacheData.conn || []
                }
              : {};

            devices.push({
              deviceId,
              deviceName: "",
              status: -1,
              data: {
                system: {
                  rNet: rNetData
                }
              },
              supports: [],
              rate: "-",
              error: true
            });
          }
        } catch (e) {
          console.error(`Error processing device ${deviceId}:`, e);
          // 即使出错，也尝试使用缓存数据
          const cacheData = cacheDataMap.get(deviceId);
          const rNetData = cacheData
            ? {
                stun: cacheData.stun,
                wan: cacheData.wan,
                map: cacheData.mappingIp,
                allowedIPs: cacheData.allowedIps?.join(", ") || "",
                peer: cacheData.conn || []
              }
            : {};

          devices.push({
            deviceId,
            deviceName: "",
            status: -1,
            data: {
              system: {
                rNet: rNetData
              }
            },
            supports: [],
            rate: "-",
            error: true
          });
        }
      })
    );
    return res;
  } catch (error) {
    console.error("Error in getRnetDevice:", error);
    return null;
  }
};

// 转换字节为可读格式
export const convertBytes = (bytes: number): string => {
  if (bytes === 0) return "0 B/s";
  const k = 1024;
  const sizes = ["B/s", "KB/s", "MB/s", "GB/s", "TB/s"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 轮询更新设备数据
export const pollRnetConfigs = async (devices: any[]) => {
  try {
    console.log("Polling for data...");

    // 防御性地遍历设备
    if (!devices || !Array.isArray(devices)) {
      console.warn("Invalid devices data in pollRnetConfigs");
      return false;
    }

    // 存储设备状态数据的结果
    const resultsForPeers: any[] = [];

    // 获取设备数据
    for (const item of devices) {
      try {
        if (!item || !item.deviceId) continue;

        const rnetConfig = await getRnetDeviceStatusJwe({ deviceId: item.deviceId });
        if (rnetConfig && rnetConfig.code === "200") {
          // 更新设备数据
          item.data = rnetConfig.data;

          // 添加到结果数组
          resultsForPeers.push(rnetConfig.data);
        }
      } catch (error) {
        console.error(`Error fetching rnet config for device ${item?.deviceId}:`, error);
      }
    }

    // 初始化新的节点数据
    const newPeers: any = {};

    // 遍历所有设备 rnetConfig 结果，存入 newPeers
    resultsForPeers.forEach(resItem => {
      if (resItem && resItem.system && resItem.system.rNet && resItem.system.rNet.peer) {
        resItem.system.rNet.peer.forEach((p: any) => {
          // 累加相同 deviceId 的流量数据
          if (!newPeers[p.deviceId]) {
            newPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
          }
          newPeers[p.deviceId].rxByte = Number(p.rxByte) || 0;
          newPeers[p.deviceId].txByte = Number(p.txByte) || 0;
          newPeers[p.deviceId].time = Number(p.time) || 0;
          newPeers[p.deviceId].status = String(p.status) || "SUCCESS";
        });
      }
    });

    // 更新每个节点的速率
    devices.forEach(device => {
      const rateData = newPeers[device.deviceId];
      const oldPeer = globalPeers[device.deviceId];
      let rateStr = "0 B/s";
      if (rateData) {
        // 计算速率差值
        const rxDiff = rateData.rxByte - (oldPeer?.rxByte || 0);
        const txDiff = rateData.txByte - (oldPeer?.txByte || 0);
        const timeDiff = rateData.time - (oldPeer?.time || 0);

        // 确保时间差不为0,并且流量差值为正数
        if (timeDiff > 0) {
          const total = Math.max(0, (rxDiff + txDiff) / timeDiff);
          if (isNumber(total)) {
            rateStr = convertBytes(total) + "/s";
          }
        }
      }
      device.rate = rateStr;
    });

    // 将新数据保存到全局数据
    resultsForPeers.forEach(resItem => {
      if (resItem && resItem.system && resItem.system.rNet && resItem.system.rNet.peer) {
        resItem.system.rNet.peer.forEach((p: any) => {
          if (!globalPeers[p.deviceId]) {
            globalPeers[p.deviceId] = { rxByte: 0, txByte: 0 };
          }
          globalPeers[p.deviceId].rxByte = Number(p.rxByte) || 0;
          globalPeers[p.deviceId].txByte = Number(p.txByte) || 0;
          globalPeers[p.deviceId].time = Number(p.time) || 0;
        });
      }
    });

    return true;
  } catch (error) {
    console.error("Error in pollRnetConfigs:", error);
    return false;
  }
};
