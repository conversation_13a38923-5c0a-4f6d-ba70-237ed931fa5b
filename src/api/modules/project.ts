import http from "@/api";
import { ResPage, User } from "@/api/interface";
import { PORT1 } from "@/api/config/servicePort";
import { Project } from "@/api/interface/project";
import { encryptData } from "@/utils/jwe";
import { useGlobalStore } from "@/stores/modules/global";
import { computed } from "vue";
import { removeEmptyValues } from "@/utils";

export const getUserGroup = () => {
  return http.get<User.ResDepartment[]>(PORT1 + `/group/queryGroupForTree`, {}, { cancel: false });
};

// 获取项目设备列表
export const getDeviceListByGroupId = (params: Project.ReqProjectParams) => {
  return http.get<ResPage<User.ResUserList>>(PORT1 + `/device/queryDevicesByGroupIdByPage`, params);
};

// 获取项目设备列表
export const getDeviceType = () => {
  return http.get<Project.ResConfigList>(PORT1 + `/config/list?configName=device_type`, {}, { cancel: false });
};

// 获取项目设备统计信息
export const getDeviceStatistics = (params: { groupId: string }) => {
  return http.get<Project.DeviceStatistic[]>(PORT1 + `/device/queryDevicesByGroupIdCount`, params);
};

// 获取浏览器语言
const globalStore = useGlobalStore();

/**
 * @description：设备状态枚举
 */
export const deviceStatusEnum = computed(() => {
  const isChinese = globalStore.language === "zh";
  return [
    {
      label: isChinese ? "离线" : "Offline",
      value: 1,
      tagType: "danger"
    },
    {
      label: isChinese ? "在线" : "Online",
      value: 0,
      tagType: "success"
    }
  ];
});

// 新增项目
export const addProject = (params: { id: string }) => {
  return http.post(PORT1 + `/group/saveGroup`, params);
};

// 编辑项目
export const editProject = (params: { id: string }) => {
  return http.put(PORT1 + `/group/updateGroup`, params);
};

// 删除项目
export const delProject = (params: { groupId: string }) => {
  return http.post(PORT1 + `/group/deleteGroup`, params);
};

// 绑定设备
export const bindDevice = (params: { cmd: 2; deviceId: string; password: string }) => {
  params.cmd = 2; // 绑定设备
  return http.post(PORT1 + `/device/remoteBind`, params);
};

export const updateDevice = (params: {}) => {
  // 更新设备信息
  return http.post(PORT1 + `/device/renameDevice`, params);
};

// 查询所有设备型号
export const getDeviceModel = () => {
  return http.get<Project.ResDeviceModel[]>(PORT1 + `/deviceModel/list`, {}, { cancel: false });
};

// 查询所有设备型号
export const getDeviceConfig = (params: Project.ReqConfigParams) => {
  return http.post<any>(PORT1 + `/device/getConfiguration`, params);
};

// const privateKey = import.meta.env.VITE_JWE_PRIVATE_KEY;

// 通过JWE加密获取设备配置信息
export const getDeviceConfigJwe = async (params: Project.ReqConfigParams, loading: boolean = true) => {
  const publicKey = import.meta.env.VITE_JWE_PUBLIC_KEY;
  console.log("开始加密:", Date.now());
  const paramsJwe = await encryptData(params, publicKey);
  // const originalData = await decryptData(paramsJwe, privateKey);
  console.log("加密完成:", Date.now(), "加密数据:", paramsJwe);
  // console.log("🚀 ~ file: project.ts ~ getDeviceConfigJwe ~ originalData", originalData);
  return http.post<any>(PORT1 + `/jwe/getConfiguration`, paramsJwe, { cancel: false, loading: loading });
};

// 推送设备配置
export const pushDeviceConfig = (params: Project.ReqConfigParams) => {
  return http.post<any>(PORT1 + `/device/pushConfiguration`, params);
};

// 推送设备配置
export const pushDeviceConfigJwe = async (params: Project.ReqConfigParams) => {
  const publicKey = import.meta.env.VITE_JWE_PUBLIC_KEY;
  // 移除params中的所有空值
  const cleanParams = removeEmptyValues(params);
  console.log("🚀 ~ file: project.ts ~ pushDeviceConfigJwe ~ cleanParams", JSON.stringify(cleanParams));
  const paramsJwe = await encryptData(cleanParams, publicKey);
  return http.post<any>(PORT1 + `/jwe/pushConfiguration`, paramsJwe);
};

export const renameDevice = (params: { deviceName: string; deviceId: string }) => {
  return http.post(PORT1 + `/device/renameDevice`, params);
};

export const unbindDevice = async (params: { userId: string; deviceId: string }) => {
  return http.post(PORT1 + `/device/unbind`, { ...params, cmd: 11 });
};
