<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>移动端工具栏修复测试 - <PERSON><PERSON>-Admin</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        color: #333333;
        background: #f5f7fa;
      }
      .container {
        max-width: 1200px;
        padding: 20px;
        margin: 0 auto;
      }
      .demo-section {
        margin-bottom: 30px;
        overflow: hidden;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
      }
      .demo-header {
        padding: 15px 20px;
        font-size: 16px;
        font-weight: 600;
        color: white;
        background: #409eff;
      }
      .demo-content {
        padding: 20px;
      }

      /* 模拟 <PERSON><PERSON>-<PERSON><PERSON> 的 Header 布局 */
      .mock-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 55px;
        padding: 0 15px 0 0;
        margin-bottom: 15px;
        background: #ffffff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
      }
      .mock-logo {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        width: auto;
        min-width: 140px;
        margin-right: 20px;
      }
      .mock-logo-img {
        width: 28px;
        height: 28px;
        margin-right: 6px;
        background: #409eff;
        border-radius: 50%;
      }
      .mock-logo-text {
        font-size: 21.5px;
        font-weight: bold;
        color: #409eff;
        white-space: nowrap;
      }
      .mock-menu {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        min-width: 0;
        height: 100%;
        margin: 0 10px;
        background: #f5f7fa;
        border-radius: 4px;
      }
      .mock-toolbar {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: flex-end;
        min-width: 200px;
        padding-right: 25px;
      }
      .mock-toolbar-icons {
        display: flex;
        align-items: center;
      }
      .mock-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        margin-left: 21px;
        cursor: pointer;
        background: #e4e7ed;
        border-radius: 8px;
        transition: all 0.3s ease;
      }
      .mock-icon:hover {
        color: white;
        background: #409eff;
        transform: scale(1.1);
      }
      .mock-username {
        flex-shrink: 0;
        max-width: 100px;
        margin: 0 20px;
        overflow: hidden;
        font-size: 15px;
        color: #606266;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .mock-avatar {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        font-weight: bold;
        color: white;
        cursor: pointer;
        background: #409eff;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
      .mock-avatar:hover {
        box-shadow: 0 0 0 2px #b3d8ff;
        transform: scale(1.05);
      }

      /* 响应式断点测试 */
      .breakpoint-info {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1000;
        padding: 5px 10px;
        font-size: 12px;
        color: white;
        background: #409eff;
        border-radius: 4px;
      }

      /* 768px 以下 - 平板端 */
      @media screen and (width <= 768px) {
        .breakpoint-info::after {
          content: " - 平板端";
        }
        .mock-header {
          padding: 0 10px 0 0;
        }
        .mock-logo {
          min-width: 100px;
          margin-right: 15px;
        }
        .mock-logo-text {
          font-size: 18px;
        }
        .mock-menu {
          display: none;
        }
        .mock-toolbar {
          min-width: 180px;
          padding-right: 15px;
        }
        .mock-icon {
          width: 32px;
          height: 32px;
          margin-left: 12px;
        }
        .mock-username {
          max-width: 80px;
          margin: 0 10px;
          font-size: 14px;
        }
        .mock-avatar {
          width: 36px;
          height: 36px;
        }
      }

      /* 480px 以下 - 手机端 */
      @media screen and (width <= 480px) {
        .breakpoint-info::after {
          content: " - 手机端";
        }
        .mock-header {
          padding: 0 8px 0 0;
        }
        .mock-logo {
          min-width: 80px;
          margin-right: 10px;
        }
        .mock-logo-img {
          width: 24px;
          height: 24px;
        }
        .mock-logo-text {
          font-size: 16px;
        }
        .mock-toolbar {
          min-width: 160px;
          padding-right: 10px;
        }
        .mock-icon {
          width: 28px;
          height: 28px;
          margin-left: 8px;
        }
        .mock-username {
          display: none;
        }
        .mock-avatar {
          width: 32px;
          height: 32px;
        }
      }

      /* 360px 以下 - 超小屏幕 */
      @media screen and (width <= 360px) {
        .breakpoint-info::after {
          content: " - 超小屏幕";
        }
        .mock-logo-text {
          display: none;
        }
        .mock-toolbar {
          min-width: 140px;
        }
        .mock-icon:nth-child(n + 4) {
          display: none;
        }
        .mock-avatar {
          width: 28px;
          height: 28px;
        }
      }
      .problem-list {
        padding: 0;
        list-style: none;
      }
      .problem-list li {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eeeeee;
      }
      .problem-list li:last-child {
        border-bottom: none;
      }
      .status {
        padding: 2px 8px;
        margin-right: 10px;
        font-size: 12px;
        font-weight: bold;
        border-radius: 12px;
      }
      .status.fixed {
        color: white;
        background: #67c23a;
      }
      .status.issue {
        color: white;
        background: #f56c6c;
      }
      .solution-list {
        padding: 0;
        list-style: none;
      }
      .solution-list li {
        display: flex;
        align-items: flex-start;
        padding: 8px 0;
        border-bottom: 1px solid #eeeeee;
      }
      .solution-list li:last-child {
        border-bottom: none;
      }
      .solution-number {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        margin-right: 10px;
        font-size: 12px;
        font-weight: bold;
        color: white;
        background: #409eff;
        border-radius: 50%;
      }
      .responsive-test {
        padding: 15px;
        margin-top: 20px;
        background: #f8f9fa;
        border-left: 4px solid #409eff;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div class="breakpoint-info">当前屏幕宽度: <span id="screenWidth"></span>px</div>

    <div class="container">
      <!-- 修复前后对比 -->
      <div class="demo-section">
        <div class="demo-header">🎯 移动端工具栏显示修复效果演示</div>
        <div class="demo-content">
          <h3>修复后的Header布局（支持响应式）</h3>
          <div class="mock-header">
            <div class="mock-logo">
              <div class="mock-logo-img"></div>
              <span class="mock-logo-text">Geeker-Admin</span>
            </div>
            <div class="mock-menu">导航菜单区域</div>
            <div class="mock-toolbar">
              <div class="mock-toolbar-icons">
                <div class="mock-icon">📐</div>
                <div class="mock-icon">🌐</div>
                <div class="mock-icon">🔍</div>
                <div class="mock-icon">🌙</div>
                <div class="mock-icon">⚙️</div>
                <div class="mock-icon">💬</div>
                <div class="mock-icon">⛶</div>
              </div>
              <span class="mock-username">管理员</span>
              <div class="mock-avatar">头</div>
            </div>
          </div>

          <div class="responsive-test">
            <strong>响应式测试提示：</strong>
            <p>请调整浏览器窗口大小或使用开发者工具的设备模拟来测试不同屏幕尺寸下的显示效果</p>
          </div>
        </div>
      </div>

      <!-- 问题分析 -->
      <div class="demo-section">
        <div class="demo-header">🔍 移动端工具栏问题分析</div>
        <div class="demo-content">
          <h3>发现的问题</h3>
          <ul class="problem-list">
            <li>
              <span class="status fixed">已修复</span>
              <span>Logo区域固定宽度210px，占用过多空间导致工具栏被挤压</span>
            </li>
            <li>
              <span class="status fixed">已修复</span>
              <span>工具栏没有设置flex-shrink: 0，容易被压缩</span>
            </li>
            <li>
              <span class="status fixed">已修复</span>
              <span>移动端菜单仍显示，占用宝贵的屏幕空间</span>
            </li>
            <li>
              <span class="status fixed">已修复</span>
              <span>用户头像和用户名在小屏幕上显示太大</span>
            </li>
            <li>
              <span class="status fixed">已修复</span>
              <span>工具栏图标间距过大，在移动端显示不下</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 解决方案 -->
      <div class="demo-section">
        <div class="demo-header">✅ 解决方案实施</div>
        <div class="demo-content">
          <h3>技术修复要点</h3>
          <ul class="solution-list">
            <li>
              <span class="solution-number">1</span>
              <div><strong>Logo区域优化：</strong>将固定宽度210px改为auto，设置min-width并添加flex-shrink: 0</div>
            </li>
            <li>
              <span class="solution-number">2</span>
              <div><strong>工具栏空间保护：</strong>设置min-width: 200px和flex-shrink: 0，防止被压缩</div>
            </li>
            <li>
              <span class="solution-number">3</span>
              <div><strong>移动端菜单隐藏：</strong>在768px以下屏幕隐藏导航菜单，释放空间给工具栏</div>
            </li>
            <li>
              <span class="solution-number">4</span>
              <div><strong>渐进式响应设计：</strong>针对768px、480px、360px三个断点优化布局</div>
            </li>
            <li>
              <span class="solution-number">5</span>
              <div><strong>超小屏幕优化：</strong>360px以下隐藏部分图标和文字，只保留核心功能</div>
            </li>
            <li>
              <span class="solution-number">6</span>
              <div><strong>用户体验增强：</strong>头像和图标添加hover效果，提升交互体验</div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 技术细节 -->
      <div class="demo-section">
        <div class="demo-header">🛠️ 关键技术实现</div>
        <div class="demo-content">
          <h3>CSS关键代码片段</h3>
          <pre
            style="padding: 15px; overflow-x: auto; background: #f5f7fa; border-radius: 4px"
          ><code>// LayoutTransverse header样式
.el-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .logo {
    width: auto;              // 🔧 从固定210px改为auto
    min-width: 140px;         // ✅ 设置最小宽度
    flex-shrink: 0;           // ✅ 防止压缩
  }
  
  .tool-bar-ri {
    flex-shrink: 0;           // ✅ 工具栏不被压缩
    min-width: 200px;         // ✅ 确保最小显示宽度
  }
}

// 移动端优化
@media screen and (max-width: 768px) {
  .el-menu { display: none; } // 🔧 隐藏菜单释放空间
  .tool-bar-ri { min-width: 180px; }
}

@media screen and (max-width: 480px) {
  .username { display: none; } // 🔧 隐藏用户名
  .tool-bar-ri { min-width: 160px; }
}

@media screen and (max-width: 360px) {
  .header-icon > *:nth-child(n+4) {
    display: none;             // 🔧 只显示前3个图标
  }
}</code></pre>
        </div>
      </div>
    </div>

    <script>
      // 显示当前屏幕宽度
      function updateScreenWidth() {
        document.getElementById("screenWidth").textContent = window.innerWidth;
      }

      updateScreenWidth();
      window.addEventListener("resize", updateScreenWidth);

      // 添加交互效果
      document.querySelectorAll(".mock-icon, .mock-avatar").forEach(element => {
        element.addEventListener("click", function () {
          this.style.transform = "scale(0.95)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });
      });
    </script>
  </body>
</html>
