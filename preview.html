<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>页尾优化预览</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      .main-content {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
      }
      .preview-card {
        max-width: 600px;
        padding: 40px;
        text-align: center;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
      }
      .preview-title {
        margin-bottom: 16px;
        font-size: 28px;
        font-weight: 600;
        color: #2c3e50;
      }
      .preview-description {
        margin-bottom: 30px;
        line-height: 1.6;
        color: #7f8c8d;
      }
      .features-list {
        padding: 20px;
        margin-bottom: 30px;
        text-align: left;
        background: #f8f9fa;
        border-radius: 8px;
      }
      .features-list h3 {
        margin-bottom: 15px;
        font-size: 18px;
        color: #2c3e50;
      }
      .features-list ul {
        list-style: none;
      }
      .features-list li {
        position: relative;
        padding: 8px 0;
        padding-left: 24px;
        color: #34495e;
      }
      .features-list li::before {
        position: absolute;
        top: 8px;
        left: 0;
        content: "✨";
      }

      /* 通用页尾样式 */
      .footer {
        height: 50px;
        padding: 0 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-top: 1px solid #e9ecef;
        box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
        transition: all 0.3s ease;
      }
      .footer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1400px;
        height: 100%;
        margin: 0 auto;
      }
      .footer-left {
        display: flex;
        align-items: center;
      }
      .beian-link {
        display: flex;
        gap: 6px;
        align-items: center;
        font-size: 13px;
        color: #6c757d;
        text-decoration: none;
        letter-spacing: 0.3px;
        transition: all 0.3s ease;
      }
      .beian-link::before {
        font-size: 14px;
        content: "🏛️";
        opacity: 0.8;
      }
      .beian-link:hover {
        color: #007bff;
        transform: translateX(2px);
      }
      .beian-link:hover::before {
        opacity: 1;
        transform: scale(1.1);
      }
      .copyright {
        font-size: 13px;
        color: #495057;
        letter-spacing: 0.2px;
        opacity: 0.8;
        transition: opacity 0.3s ease;
      }
      .copyright:hover {
        opacity: 1;
      }

      /* 登录页面页尾样式 */
      .login-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 999;
        width: 100vw;
        padding: 16px 20px;
        background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 95%) 30%, rgb(255 255 255 / 98%) 100%);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgb(255 255 255 / 20%);
        transition: all 0.3s ease;
      }
      .login-footer:hover {
        background: linear-gradient(180deg, rgb(255 255 255 / 0%) 0%, rgb(255 255 255 / 98%) 20%, rgb(255 255 255 / 100%) 100%);
      }
      .login-footer .beian-link {
        display: inline-flex;
        gap: 6px;
        align-items: center;
        justify-content: center;
        width: 100%;
        font-size: 13px;
        font-weight: 500;
        color: #666666;
        text-align: center;
        text-decoration: none;
        letter-spacing: 0.3px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .login-footer .beian-link::before {
        font-size: 14px;
        content: "🏛️";
        opacity: 0.7;
        transition: all 0.3s ease;
      }
      .login-footer .beian-link:hover {
        color: #387dde;
        text-decoration: none;
        filter: drop-shadow(0 2px 4px rgb(56 125 222 / 20%));
        transform: translateY(-1px);
      }
      .login-footer .beian-link:hover::before {
        opacity: 1;
        transform: scale(1.1);
      }
      .login-footer .copyright {
        margin-top: 8px;
        font-size: 12px;
        font-weight: 400;
        color: #888888;
        text-align: center;
        letter-spacing: 0.2px;
        opacity: 0.9;
        transition: all 0.3s ease;
      }
      .login-footer .copyright:hover {
        color: #666666;
        opacity: 1;
      }

      /* 响应式设计 */
      @media screen and (width <= 768px) {
        .footer {
          height: auto;
          padding: 12px 16px;
        }
        .footer-content {
          flex-direction: column;
          gap: 8px;
          text-align: center;
        }
        .footer-left,
        .footer-right {
          width: 100%;
        }
        .beian-link,
        .copyright {
          font-size: 12px !important;
        }
        .login-footer {
          padding: 12px 16px;
        }
        .login-footer .beian-link {
          gap: 4px;
          font-size: 12px;
        }
        .login-footer .copyright {
          margin-top: 6px;
          font-size: 11px;
        }
      }

      @media screen and (width <= 480px) {
        .footer {
          padding: 10px 12px;
        }
        .beian-link,
        .copyright {
          font-size: 11px !important;
        }
        .login-footer {
          padding: 10px 12px;
        }
        .login-footer .beian-link {
          flex-direction: column;
          gap: 2px;
          font-size: 11px;
        }
        .login-footer .copyright {
          margin-top: 4px;
          font-size: 10px;
          line-height: 1.4;
        }
      }
      .demo-section {
        padding: 20px;
        margin: 30px 0;
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        border-radius: 8px;
      }
      .demo-title {
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
      .demo-footer {
        overflow: hidden;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
      }
      .language-switch {
        margin: 20px 0;
        text-align: center;
      }
      .language-btn {
        padding: 8px 16px;
        margin: 0 5px;
        color: white;
        cursor: pointer;
        background: #007bff;
        border: none;
        border-radius: 4px;
        transition: background 0.3s ease;
      }
      .language-btn:hover {
        background: #0056b3;
      }
      .language-btn.active {
        background: #28a745;
      }
    </style>
  </head>
  <body>
    <div class="main-content">
      <div class="preview-card">
        <h1 class="preview-title">🎨 页尾组件优化完成</h1>
        <p class="preview-description">我已经完成了页尾组件的全面优化，提升了用户体验和视觉效果。</p>

        <div class="features-list">
          <h3>✨ 优化特性</h3>
          <ul>
            <li>修复了SCSS导入错误，提高代码稳定性</li>
            <li>优化了页尾布局，改善视觉层次</li>
            <li>添加了国际化支持(中文/英文)</li>
            <li>增强了响应式设计，支持移动端适配</li>
            <li>添加了精美的动画效果和交互反馈</li>
            <li>改善了暗色主题适配</li>
            <li>优化了登录页面页尾的视觉效果</li>
            <li>添加了图标和视觉装饰元素</li>
          </ul>
        </div>

        <div class="language-switch">
          <button class="language-btn active" onclick="setLanguage('zh')">中文</button>
          <button class="language-btn" onclick="setLanguage('en')">English</button>
        </div>

        <div class="demo-section">
          <div class="demo-title">📱 通用页尾样式 (适用于后台管理页面)</div>
          <div class="demo-footer">
            <div class="footer">
              <div class="footer-content">
                <div class="footer-left">
                  <a class="beian-link" href="https://beian.miit.gov.cn" target="_blank" title="ICP备案查询">
                    <span id="beian-text">粤ICP备2023072379号</span>
                  </a>
                </div>
                <div class="footer-right">
                  <span class="copyright" id="copyright-text"> 2023-2025 © 深圳市恒源科技发展有限公司版权所有 </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="demo-section">
          <div class="demo-title">🔐 登录页面页尾样式 (固定在页面底部)</div>
          <div class="demo-footer">
            <div class="login-footer">
              <a class="beian-link" href="https://beian.miit.gov.cn" target="_blank" title="ICP备案查询">
                <span id="login-beian-text">粤ICP备2023072379号</span>
              </a>
              <div class="copyright" id="login-copyright-text">2023-2025 © 深圳市恒源科技发展有限公司版权所有</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 语言配置
      const i18n = {
        zh: {
          beianNumber: "粤ICP备2023072379号",
          copyright: "2023-2025 © 深圳市恒源科技发展有限公司版权所有"
        },
        en: {
          beianNumber: "粤ICP备2023072379号",
          copyright: "2023-2025 © Shenzhen Hisource Technology Development Co., Ltd. All Rights Reserved"
        }
      };

      let currentLanguage = "zh";

      function setLanguage(lang) {
        currentLanguage = lang;

        // 更新按钮状态
        document.querySelectorAll(".language-btn").forEach(btn => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");

        // 更新文本内容
        document.getElementById("beian-text").textContent = i18n[lang].beianNumber;
        document.getElementById("copyright-text").textContent = i18n[lang].copyright;
        document.getElementById("login-beian-text").textContent = i18n[lang].beianNumber;
        document.getElementById("login-copyright-text").textContent = i18n[lang].copyright;
      }

      // 添加交互演示
      document.addEventListener("DOMContentLoaded", function () {
        // 为页尾链接添加点击事件演示
        document.querySelectorAll(".beian-link").forEach(link => {
          link.addEventListener("click", function (e) {
            e.preventDefault();
            alert("页尾链接点击演示 - 实际会跳转到ICP备案查询网站");
          });
        });
      });
    </script>
  </body>
</html>
