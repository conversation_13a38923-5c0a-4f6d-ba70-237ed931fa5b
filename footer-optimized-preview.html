<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>📝 页尾布局优化对比预览</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      .main-content {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
      }
      .preview-card {
        max-width: 900px;
        padding: 40px;
        text-align: center;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgb(0 0 0 / 12%);
      }
      .preview-title {
        margin-bottom: 16px;
        font-size: 32px;
        font-weight: 700;
        color: #2c3e50;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .preview-subtitle {
        margin-bottom: 40px;
        font-size: 16px;
        line-height: 1.6;
        color: #64748b;
      }
      .comparison-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 40px;
      }
      .comparison-item {
        padding: 24px;
        background: #f8fafc;
        border: 2px solid transparent;
        border-radius: 12px;
        transition: all 0.3s ease;
      }
      .comparison-item:hover {
        border-color: #3b82f6;
        box-shadow: 0 8px 24px rgb(59 130 246 / 15%);
        transform: translateY(-2px);
      }
      .comparison-title {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: 600;
        color: #374151;
      }
      .comparison-title.before {
        color: #dc2626;
      }
      .comparison-title.after {
        color: #059669;
      }
      .demo-footer {
        overflow: hidden;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
      }

      /* 优化前的样式 */
      .footer-old {
        height: 50px;
        padding: 0 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-top: 1px solid #e9ecef;
        box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
      }
      .footer-old .footer-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 1400px;
        height: 100%;
        margin: 0 auto;
      }
      .footer-old .beian-link {
        display: flex;
        gap: 6px;
        align-items: center;
        font-size: 13px;
        color: #6c757d;
        text-decoration: none;
      }
      .footer-old .copyright {
        font-size: 13px;
        color: #495057;
      }

      /* 优化后的样式 */
      .footer-new {
        min-height: 60px;
        padding: 16px 20px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-top: 1px solid #e9ecef;
        box-shadow: 0 -2px 8px rgb(0 0 0 / 4%);
      }
      .footer-new .footer-content {
        display: flex;
        align-items: center;
        justify-content: center;
        max-width: 1400px;
        min-height: 44px;
        margin: 0 auto;
      }
      .footer-new .footer-main {
        display: flex;
        flex-direction: column;
        gap: 6px;
        align-items: center;
        text-align: center;
      }
      .footer-new .copyright {
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
        color: #1f2937;
        letter-spacing: 0.3px;
        transition: color 0.3s ease;
      }
      .footer-new .copyright:hover {
        color: #3b82f6;
      }
      .footer-new .beian-info {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .footer-new .beian-link {
        display: flex;
        gap: 4px;
        align-items: center;
        font-size: 12px;
        color: #6b7280;
        text-decoration: none;
        letter-spacing: 0.2px;
        opacity: 0.8;
        transition: all 0.3s ease;
      }
      .footer-new .beian-link::before {
        font-size: 12px;
        content: "🏛️";
        opacity: 0.7;
      }
      .footer-new .beian-link:hover {
        color: #3b82f6;
        opacity: 1;
        transform: translateY(-1px);
      }
      .improvements-list {
        padding: 24px;
        text-align: left;
        background: #f0f9ff;
        border-left: 4px solid #0ea5e9;
        border-radius: 12px;
      }
      .improvements-list h3 {
        margin-bottom: 16px;
        font-size: 20px;
        font-weight: 600;
        color: #0f172a;
      }
      .improvements-list ul {
        list-style: none;
      }
      .improvements-list li {
        position: relative;
        padding: 8px 0 8px 28px;
        line-height: 1.6;
        color: #334155;
      }
      .improvements-list li::before {
        position: absolute;
        top: 8px;
        left: 0;
        font-size: 16px;
        content: "✅";
      }
      .language-switch {
        margin: 30px 0;
        text-align: center;
      }
      .language-btn {
        padding: 10px 20px;
        margin: 0 8px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background: #3b82f6;
        border: none;
        border-radius: 6px;
        transition: all 0.3s ease;
      }
      .language-btn:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }
      .language-btn.active {
        background: #059669;
        box-shadow: 0 4px 12px rgb(5 150 105 / 30%);
      }

      @media screen and (width <= 768px) {
        .comparison-container {
          grid-template-columns: 1fr;
          gap: 20px;
        }
        .preview-card {
          padding: 24px;
        }
        .preview-title {
          font-size: 24px;
        }
      }
    </style>
  </head>
  <body>
    <div class="main-content">
      <div class="preview-card">
        <h1 class="preview-title">📝 页尾布局优化完成</h1>
        <p class="preview-subtitle">版权声明和备案信息的布局已经优化，采用更和谐的居中垂直布局</p>

        <div class="comparison-container">
          <div class="comparison-item">
            <h3 class="comparison-title before">❌ 优化前 - 左右分布</h3>
            <div class="demo-footer">
              <div class="footer-old">
                <div class="footer-content">
                  <div class="footer-left">
                    <a class="beian-link" href="#" onclick="return false;">
                      🏛️ <span id="old-beian">粤ICP备2023072379号</span>
                    </a>
                  </div>
                  <div class="footer-right">
                    <span class="copyright" id="old-copyright"> 2023-2025 © 深圳市众通源科技发展有限公司版权所有 </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="comparison-item">
            <h3 class="comparison-title after">✅ 优化后 - 居中垂直</h3>
            <div class="demo-footer">
              <div class="footer-new">
                <div class="footer-content">
                  <div class="footer-main">
                    <div class="copyright" id="new-copyright">2023-2025 © 深圳市众通源科技发展有限公司版权所有</div>
                    <div class="beian-info">
                      <a class="beian-link" href="#" onclick="return false;">
                        <span id="new-beian">粤ICP备2023072379号</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="language-switch">
          <button class="language-btn active" onclick="setLanguage('zh')">中文</button>
          <button class="language-btn" onclick="setLanguage('en')">English</button>
        </div>

        <div class="improvements-list">
          <h3>🎯 主要改进点</h3>
          <ul>
            <li><strong>布局更和谐</strong>：从左右分布改为居中垂直布局，视觉更平衡</li>
            <li><strong>层次更分明</strong>：版权信息在上方更突出，备案信息在下方作为辅助</li>
            <li><strong>字体层级优化</strong>：版权信息使用更大字体，备案信息使用较小字体</li>
            <li><strong>颜色对比改善</strong>：版权信息颜色更深，备案信息颜色较浅，层次清晰</li>
            <li><strong>间距优化</strong>：增加页尾高度和内容间距，视觉更舒适</li>
            <li><strong>交互体验提升</strong>：添加悬停效果，提供良好的用户反馈</li>
            <li><strong>响应式优化</strong>：在各种设备上都保持良好的显示效果</li>
          </ul>
        </div>
      </div>
    </div>

    <script>
      // 语言配置
      const i18n = {
        zh: {
          beianNumber: "粤ICP备2023072379号",
          copyright: "2023-2025 © 深圳市众通源科技发展有限公司版权所有"
        },
        en: {
          beianNumber: "粤ICP备2023072379号",
          copyright: "2023-2025 © Shenzhen Zhongtong Technology Development Co., Ltd. All Rights Reserved"
        }
      };

      let currentLanguage = "zh";

      function setLanguage(lang) {
        currentLanguage = lang;

        // 更新按钮状态
        document.querySelectorAll(".language-btn").forEach(btn => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");

        // 更新文本内容
        document.getElementById("old-beian").textContent = i18n[lang].beianNumber;
        document.getElementById("old-copyright").textContent = i18n[lang].copyright;
        document.getElementById("new-beian").textContent = i18n[lang].beianNumber;
        document.getElementById("new-copyright").textContent = i18n[lang].copyright;
      }

      // 添加交互演示
      document.addEventListener("DOMContentLoaded", function () {
        // 为页尾链接添加点击事件演示
        document.querySelectorAll(".beian-link").forEach(link => {
          link.addEventListener("click", function (e) {
            e.preventDefault();
            alert("🎉 页尾链接点击演示 - 实际会跳转到ICP备案查询网站");
          });
        });

        // 添加一些有趣的交互
        document.querySelectorAll(".comparison-item").forEach(item => {
          item.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-4px) scale(1.02)";
          });

          item.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0) scale(1)";
          });
        });
      });
    </script>
  </body>
</html>
