<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>移动端登录测试</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        min-height: 100vh;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      .container {
        max-width: 1200px;
        padding: 30px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgb(0 0 0 / 12%);
      }
      .title {
        margin-bottom: 30px;
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .device-simulator {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
      }
      .device {
        position: relative;
        padding: 20px;
        background: #1a1a1a;
        border-radius: 30px;
        box-shadow: 0 20px 40px rgb(0 0 0 / 30%);
      }
      .device::before {
        position: absolute;
        top: 8px;
        left: 50%;
        width: 50px;
        height: 4px;
        content: "";
        background: #444444;
        border-radius: 2px;
        transform: translateX(-50%);
      }
      .device-screen {
        position: relative;
        min-height: 600px;
        padding: 20px;
        overflow: hidden;
        background: white;
        border-radius: 20px;
      }
      .device-title {
        margin-bottom: 20px;
        font-size: 14px;
        color: #666666;
        text-align: center;
      }

      /* 登录表单样式 */
      .login-form {
        max-width: 350px;
        padding: 30px 20px;
        margin: 0 auto;
        background: rgb(255 255 255 / 90%);
        border-radius: 16px;
        box-shadow: 0 10px 30px rgb(0 0 0 / 10%);
      }
      .logo-section {
        margin-bottom: 30px;
        text-align: center;
      }
      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        margin: 0 auto 15px;
        font-size: 24px;
        font-weight: bold;
        color: white;
        background: linear-gradient(135deg, #409eff, #67c23a);
        border-radius: 50%;
      }
      .logo-text {
        font-size: 24px;
        font-weight: bold;
        color: #2c3e50;
      }
      .form-group {
        margin-bottom: 20px;
      }
      .form-input {
        width: 100%;
        padding: 12px 16px;
        font-size: 16px;
        background: rgb(255 255 255 / 80%);
        border: 2px solid #e1e8ed;
        border-radius: 12px;
        transition: all 0.3s ease;
      }
      .form-input:focus {
        border-color: #409eff;
        outline: none;
        box-shadow: 0 0 0 3px rgb(64 158 255 / 10%);
      }
      .login-buttons {
        display: flex;
        gap: 12px;
        justify-content: space-between;
        margin-top: 25px;
      }
      .btn {
        flex: 1;
        min-height: 44px;
        padding: 12px 20px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
      }
      .btn-reset {
        color: #606266;
        background: #f5f7fa;
      }
      .btn-reset:hover {
        background: #e4e7ed;
        transform: translateY(-2px);
      }
      .btn-primary {
        color: white;
        background: #409eff;
      }
      .btn-primary:hover {
        background: #337ecc;
        box-shadow: 0 5px 15px rgb(64 158 255 / 30%);
        transform: translateY(-2px);
      }
      .login-links {
        padding-top: 20px;
        margin-top: 20px;
        text-align: center;
        border-top: 1px solid #e1e8ed;
      }
      .login-links p {
        margin-bottom: 10px;
        font-size: 14px;
        color: #666666;
      }
      .login-link {
        display: inline-block;
        padding: 5px 10px;
        margin: 5px 8px;
        font-size: 14px;
        font-weight: 500;
        color: #409eff;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.3s ease;
      }
      .login-link:hover {
        background: rgb(64 158 255 / 10%);
        transform: translateY(-1px);
      }

      /* 弹窗样式 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        display: none;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgb(0 0 0 / 50%);
      }
      .modal {
        width: 90%;
        max-width: 500px;
        max-height: 80vh;
        padding: 30px;
        overflow-y: auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgb(0 0 0 / 30%);
        transition: all 0.3s ease;
        transform: scale(0.9);
      }
      .modal.show {
        transform: scale(1);
      }
      .modal-header {
        margin-bottom: 25px;
        text-align: center;
      }
      .modal-title {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
      }
      .modal-buttons {
        display: flex;
        gap: 12px;
        margin-top: 25px;
      }
      .close-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        font-size: 24px;
        color: #999999;
        cursor: pointer;
        background: none;
        border: none;
      }

      /* 移动端优化 */
      @media screen and (width <= 768px) {
        .device-simulator {
          grid-template-columns: 1fr;
        }
        .login-buttons {
          flex-direction: column;
          gap: 12px;
        }
        .btn {
          width: 100%;
        }
        .login-link {
          display: block;
          margin: 8px 0;
          text-align: center;
        }
        .modal {
          width: 95%;
          padding: 20px;
          margin: 20px;
        }
      }

      @media screen and (width <= 480px) {
        .container {
          padding: 20px;
        }
        .login-form {
          padding: 25px 15px;
        }
        .logo {
          width: 50px;
          height: 50px;
          font-size: 20px;
        }
        .logo-text {
          font-size: 20px;
        }
        .modal {
          width: 98%;
          padding: 15px;
        }
      }
      .test-results {
        padding: 20px;
        margin-bottom: 30px;
        background: #f0f9ff;
        border-left: 4px solid #0ea5e9;
        border-radius: 8px;
      }
      .test-results h3 {
        margin-bottom: 15px;
        font-size: 18px;
        color: #0f172a;
      }
      .test-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        margin-bottom: 10px;
      }
      .test-item::before {
        margin-right: 10px;
        font-size: 16px;
        content: "✅";
      }
      .viewport-info {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 100;
        padding: 8px 12px;
        font-size: 12px;
        color: white;
        background: rgb(0 0 0 / 80%);
        border-radius: 6px;
      }
    </style>
  </head>
  <body>
    <div class="viewport-info" id="viewportInfo">视口: <span id="viewport">计算中...</span></div>

    <div class="container">
      <h1 class="title">🔧 移动端登录功能测试</h1>

      <div class="test-results">
        <h3>测试要点</h3>
        <div class="test-item">登录按钮在移动端正确显示（横排 → 竖排）</div>
        <div class="test-item">注册和找回密码链接在小屏幕下换行显示</div>
        <div class="test-item">弹窗在移动端自适应宽度和高度</div>
        <div class="test-item">表单输入框在移动设备上易于操作</div>
        <div class="test-item">所有交互元素的触摸目标 ≥ 44px</div>
      </div>

      <div class="device-simulator">
        <!-- 桌面端预览 -->
        <div class="device">
          <div class="device-screen">
            <div class="device-title">桌面端 (1200px+)</div>
            <div class="login-form">
              <div class="logo-section">
                <div class="logo">C</div>
                <div class="logo-text">CloudIot</div>
              </div>

              <div class="form-group">
                <input type="text" class="form-input" placeholder="用户名" value="admin" />
              </div>

              <div class="form-group">
                <input type="password" class="form-input" placeholder="密码" value="••••••••" />
              </div>

              <div class="login-buttons">
                <button class="btn btn-reset">重置</button>
                <button class="btn btn-primary">登录</button>
              </div>

              <div class="login-links">
                <p>还没有账号？</p>
                <a href="#" class="login-link" onclick="showModal('register')">立即注册</a>
                <a href="#" class="login-link" onclick="showModal('forgot')">找回密码</a>
              </div>
            </div>
          </div>
        </div>

        <!-- 平板端预览 -->
        <div class="device">
          <div class="device-screen">
            <div class="device-title">平板端 (768px)</div>
            <div class="login-form">
              <div class="logo-section">
                <div class="logo">C</div>
                <div class="logo-text">CloudIot</div>
              </div>

              <div class="form-group">
                <input type="text" class="form-input" placeholder="用户名" value="admin" />
              </div>

              <div class="form-group">
                <input type="password" class="form-input" placeholder="密码" value="••••••••" />
              </div>

              <div class="login-buttons">
                <button class="btn btn-reset">重置</button>
                <button class="btn btn-primary">登录</button>
              </div>

              <div class="login-links">
                <p>还没有账号？</p>
                <a href="#" class="login-link" onclick="showModal('register')">立即注册</a>
                <a href="#" class="login-link" onclick="showModal('forgot')">找回密码</a>
              </div>
            </div>
          </div>
        </div>

        <!-- 手机端预览 -->
        <div class="device">
          <div class="device-screen">
            <div class="device-title">手机端 (480px)</div>
            <div class="login-form">
              <div class="logo-section">
                <div class="logo">C</div>
                <div class="logo-text">CloudIot</div>
              </div>

              <div class="form-group">
                <input type="text" class="form-input" placeholder="用户名" value="admin" />
              </div>

              <div class="form-group">
                <input type="password" class="form-input" placeholder="密码" value="••••••••" />
              </div>

              <div class="login-buttons">
                <button class="btn btn-reset">重置</button>
                <button class="btn btn-primary">登录</button>
              </div>

              <div class="login-links">
                <p>还没有账号？</p>
                <a href="#" class="login-link" onclick="showModal('register')">立即注册</a>
                <a href="#" class="login-link" onclick="showModal('forgot')">找回密码</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 注册弹窗 -->
    <div class="modal-overlay" id="registerModal">
      <div class="modal">
        <button class="close-btn" onclick="hideModal('register')">&times;</button>
        <div class="modal-header">
          <h2 class="modal-title">用户注册</h2>
        </div>

        <div class="form-group">
          <input type="text" class="form-input" placeholder="邮箱或手机号" />
        </div>

        <div class="form-group">
          <input type="text" class="form-input" placeholder="验证码" />
        </div>

        <div class="form-group">
          <input type="password" class="form-input" placeholder="密码" />
        </div>

        <div class="form-group">
          <input type="password" class="form-input" placeholder="确认密码" />
        </div>

        <div class="form-group">
          <input type="text" class="form-input" placeholder="昵称" />
        </div>

        <div class="modal-buttons">
          <button class="btn btn-reset" onclick="hideModal('register')">取消</button>
          <button class="btn btn-primary">确认注册</button>
        </div>
      </div>
    </div>

    <!-- 找回密码弹窗 -->
    <div class="modal-overlay" id="forgotModal">
      <div class="modal">
        <button class="close-btn" onclick="hideModal('forgot')">&times;</button>
        <div class="modal-header">
          <h2 class="modal-title">找回密码</h2>
        </div>

        <div class="form-group">
          <input type="text" class="form-input" placeholder="邮箱或手机号" />
        </div>

        <div class="form-group">
          <input type="text" class="form-input" placeholder="验证码" />
        </div>

        <div class="form-group">
          <input type="password" class="form-input" placeholder="新密码" />
        </div>

        <div class="form-group">
          <input type="password" class="form-input" placeholder="确认新密码" />
        </div>

        <div class="modal-buttons">
          <button class="btn btn-reset" onclick="hideModal('forgot')">取消</button>
          <button class="btn btn-primary">重置密码</button>
        </div>
      </div>
    </div>

    <script>
      // 显示弹窗
      function showModal(type) {
        const modal = document.getElementById(type + "Modal");
        modal.style.display = "flex";
        setTimeout(() => {
          modal.querySelector(".modal").classList.add("show");
        }, 10);
      }

      // 隐藏弹窗
      function hideModal(type) {
        const modal = document.getElementById(type + "Modal");
        modal.querySelector(".modal").classList.remove("show");
        setTimeout(() => {
          modal.style.display = "none";
        }, 300);
      }

      // 点击遮罩层关闭弹窗
      document.querySelectorAll(".modal-overlay").forEach(overlay => {
        overlay.addEventListener("click", e => {
          if (e.target === overlay) {
            const modalId = overlay.id;
            const type = modalId.replace("Modal", "");
            hideModal(type);
          }
        });
      });

      // 显示视口信息
      function updateViewportInfo() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        document.getElementById("viewport").textContent = `${width} × ${height}px`;
      }

      updateViewportInfo();
      window.addEventListener("resize", updateViewportInfo);

      // 按钮交互效果
      document.querySelectorAll(".btn").forEach(btn => {
        btn.addEventListener("click", function () {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });
      });

      // 输入框焦点效果
      document.querySelectorAll(".form-input").forEach(input => {
        input.addEventListener("focus", function () {
          this.style.transform = "scale(1.02)";
        });

        input.addEventListener("blur", function () {
          this.style.transform = "";
        });
      });

      console.log("🔧 移动端登录测试页面已加载");
      console.log("📱 请调整浏览器窗口大小来测试不同设备的显示效果");
    </script>
  </body>
</html>
