<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>移动端弹窗修复演示</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }
      body {
        min-height: 100vh;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
          sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
      .container {
        max-width: 800px;
        padding: 30px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgb(0 0 0 / 12%);
      }
      .title {
        margin-bottom: 30px;
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .fix-summary {
        padding: 20px;
        margin-bottom: 30px;
        background: #f0f9ff;
        border-left: 4px solid #0ea5e9;
        border-radius: 8px;
      }
      .fix-summary h3 {
        margin-bottom: 15px;
        font-size: 18px;
        color: #0f172a;
      }
      .fix-item {
        display: flex;
        align-items: flex-start;
        padding: 8px 0;
        margin-bottom: 12px;
      }
      .fix-item::before {
        margin-top: 2px;
        margin-right: 10px;
        font-size: 16px;
        content: "🔧";
      }
      .demo-button {
        padding: 12px 24px;
        margin: 10px 10px 10px 0;
        font-size: 16px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background: #409eff;
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
      }
      .demo-button:hover {
        background: #337ecc;
        box-shadow: 0 5px 15px rgb(64 158 255 / 30%);
        transform: translateY(-2px);
      }

      /* 修复后的弹窗样式 */
      .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        display: none;
        align-items: flex-end;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: rgb(0 0 0 / 50%);
        transition: all 0.3s ease;
      }
      .modal-overlay.show {
        display: flex;
      }
      .modal {
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 500px;
        max-height: 85vh;
        overflow: hidden;
        background: rgb(255 255 255 / 95%);
        backdrop-filter: blur(20px);
        border-radius: 16px 16px 0 0;
        box-shadow: 0 -8px 32px rgb(0 0 0 / 20%);
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        transform: translateY(100%);
      }
      .modal.show {
        transform: translateY(0);
      }
      .modal-header {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: space-between;
        padding: 20px 20px 15px;
        border-bottom: 1px solid #e5e7eb;
      }
      .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
      }
      .close-btn {
        padding: 5px;
        font-size: 24px;
        color: #999999;
        cursor: pointer;
        background: none;
        border: none;
        border-radius: 6px;
        transition: all 0.3s ease;
      }
      .close-btn:hover {
        color: #666666;
        background: #f5f5f5;
      }
      .modal-body {
        flex: 1;
        max-height: calc(85vh - 130px);
        padding: 20px;
        overflow-y: auto;
      }
      .modal-footer {
        display: flex;
        flex-shrink: 0;
        gap: 12px;
        padding: 15px 20px 20px;
        border-top: 1px solid #e5e7eb;
      }
      .form-group {
        margin-bottom: 18px;
      }
      .form-input {
        width: 100%;
        min-height: 44px;
        padding: 14px 16px;
        font-size: 16px;
        background: rgb(255 255 255 / 80%);
        border: 2px solid #e1e8ed;
        border-radius: 10px;
        transition: all 0.3s ease;
      }
      .form-input:focus {
        border-color: #409eff;
        outline: none;
        box-shadow: 0 0 0 3px rgb(64 158 255 / 10%);
      }
      .input-group {
        display: flex;
        align-items: stretch;
      }
      .input-group .form-input {
        border-right: none;
        border-radius: 10px 0 0 10px;
      }
      .input-group .send-code-btn {
        padding: 0 16px;
        font-size: 13px;
        color: #606266;
        white-space: nowrap;
        cursor: pointer;
        background: #f5f7fa;
        border: 2px solid #e1e8ed;
        border-left: none;
        border-radius: 0 10px 10px 0;
        transition: all 0.3s ease;
      }
      .input-group .send-code-btn:hover {
        background: #e4e7ed;
      }
      .btn {
        flex: 1;
        min-height: 46px;
        padding: 14px 20px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
      }
      .btn-cancel {
        color: #606266;
        background: #f5f7fa;
      }
      .btn-cancel:hover {
        background: #e4e7ed;
      }
      .btn-primary {
        color: white;
        background: #409eff;
      }
      .btn-primary:hover {
        background: #337ecc;
        box-shadow: 0 3px 10px rgb(64 158 255 / 30%);
        transform: translateY(-1px);
      }

      /* 桌面端适配 */
      @media screen and (width >= 769px) {
        .modal-overlay {
          align-items: center;
        }
        .modal {
          width: 90%;
          max-width: 500px;
          max-height: 90vh;
          border-radius: 16px;
          opacity: 0;
          transform: scale(0.9) translateY(-20px);
        }
        .modal.show {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }

      /* 小屏手机适配 */
      @media screen and (width <= 480px) {
        .modal {
          max-height: 90vh;
          border-radius: 16px 16px 0 0;
        }
        .modal-header {
          padding: 16px 16px 12px;
        }
        .modal-title {
          font-size: 17px;
        }
        .modal-body {
          max-height: calc(90vh - 120px);
          padding: 16px;
        }
        .modal-footer {
          flex-direction: column;
          gap: 10px;
          padding: 12px 16px 16px;
        }
        .btn {
          width: 100%;
          min-height: 48px;
        }
        .form-input {
          min-height: 46px;
          border-radius: 12px;
        }
        .input-group .form-input {
          border-radius: 12px 0 0 12px;
        }
        .input-group .send-code-btn {
          padding: 0 12px;
          font-size: 12px;
          border-radius: 0 12px 12px 0;
        }
      }
      .viewport-info {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 100;
        padding: 8px 12px;
        font-size: 12px;
        color: white;
        background: rgb(0 0 0 / 80%);
        border-radius: 6px;
      }
      .code-example {
        padding: 20px;
        margin: 20px 0;
        overflow-x: auto;
        font-family: Monaco, Menlo, "Ubuntu Mono", monospace;
        font-size: 14px;
        line-height: 1.5;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
      }
      .code-title {
        margin-bottom: 10px;
        font-weight: 600;
        color: #495057;
      }
    </style>
  </head>
  <body>
    <div class="viewport-info" id="viewportInfo">视口: <span id="viewport">计算中...</span></div>

    <div class="container">
      <h1 class="title">🔧 移动端弹窗修复完成</h1>

      <div class="fix-summary">
        <h3>✅ 已修复的弹窗问题</h3>
        <div class="fix-item">添加响应式弹窗宽度：移动端95%，平板90%，桌面500px</div>
        <div class="fix-item">优化弹窗定位：移动端从底部滑出，桌面居中显示</div>
        <div class="fix-item">设置最大高度限制：防止内容溢出，支持滚动</div>
        <div class="fix-item">改进按钮布局：移动端竖排全宽，桌面横排</div>
        <div class="fix-item">优化输入框尺寸：移动端44-48px触摸友好高度</div>
        <div class="fix-item">添加边框分割线：清晰的视觉层次</div>
      </div>

      <p style="margin-bottom: 20px; line-height: 1.6; color: #666666">
        点击下方按钮测试修复后的弹窗效果。在不同设备尺寸下，弹窗会自动适配最佳的显示方式。
      </p>

      <button class="demo-button" onclick="showModal('register')">测试注册弹窗</button>
      <button class="demo-button" onclick="showModal('forgot')">测试找回密码弹窗</button>

      <div class="code-example">
        <div class="code-title">主要修复代码：</div>
        <pre>
// 1. 添加响应式变量
const windowWidth = ref(window.innerWidth);
const isMobile = computed(() => windowWidth.value <= 768);
const dialogWidth = computed(() => {
  if (windowWidth.value <= 480) return "95%";
  if (windowWidth.value <= 768) return "90%";
  return "500px";
});

// 2. 弹窗配置优化
&lt;el-dialog
  :width="dialogWidth"
  :top="isMobile ? '5vh' : '15vh'"
  class="glass-dialog mobile-dialog"
&gt;

// 3. 移动端样式优化
@media screen and (width <= 768px) {
  .mobile-dialog :deep(.el-dialog) {
    position: fixed;
    bottom: 0;
    width: 100% !important;
    max-height: 85vh;
    border-radius: 12px 12px 0 0;
  }
}</pre
        >
      </div>
    </div>

    <!-- 注册弹窗 -->
    <div class="modal-overlay" id="registerModal">
      <div class="modal">
        <div class="modal-header">
          <div class="modal-title">用户注册</div>
          <button class="close-btn" onclick="hideModal('register')">&times;</button>
        </div>

        <div class="modal-body">
          <div class="form-group">
            <div class="input-group">
              <input type="text" class="form-input" placeholder="邮箱或手机号" />
              <button class="send-code-btn">发送验证码</button>
            </div>
          </div>

          <div class="form-group">
            <input type="text" class="form-input" placeholder="验证码" />
          </div>

          <div class="form-group">
            <input type="password" class="form-input" placeholder="密码" />
          </div>

          <div class="form-group">
            <input type="password" class="form-input" placeholder="确认密码" />
          </div>

          <div class="form-group">
            <input type="text" class="form-input" placeholder="昵称（可选）" />
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-cancel" onclick="hideModal('register')">取消</button>
          <button class="btn btn-primary">确认注册</button>
        </div>
      </div>
    </div>

    <!-- 找回密码弹窗 -->
    <div class="modal-overlay" id="forgotModal">
      <div class="modal">
        <div class="modal-header">
          <div class="modal-title">找回密码</div>
          <button class="close-btn" onclick="hideModal('forgot')">&times;</button>
        </div>

        <div class="modal-body">
          <div class="form-group">
            <div class="input-group">
              <input type="text" class="form-input" placeholder="邮箱或手机号" />
              <button class="send-code-btn">发送验证码</button>
            </div>
          </div>

          <div class="form-group">
            <input type="text" class="form-input" placeholder="验证码" />
          </div>

          <div class="form-group">
            <input type="password" class="form-input" placeholder="新密码" />
          </div>

          <div class="form-group">
            <input type="password" class="form-input" placeholder="确认新密码" />
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-cancel" onclick="hideModal('forgot')">取消</button>
          <button class="btn btn-primary">重置密码</button>
        </div>
      </div>
    </div>

    <script>
      // 显示弹窗
      function showModal(type) {
        const overlay = document.getElementById(type + "Modal");
        const modal = overlay.querySelector(".modal");

        overlay.classList.add("show");

        setTimeout(() => {
          modal.classList.add("show");
        }, 10);
      }

      // 隐藏弹窗
      function hideModal(type) {
        const overlay = document.getElementById(type + "Modal");
        const modal = overlay.querySelector(".modal");

        modal.classList.remove("show");

        setTimeout(() => {
          overlay.classList.remove("show");
        }, 300);
      }

      // 点击遮罩层关闭弹窗
      document.querySelectorAll(".modal-overlay").forEach(overlay => {
        overlay.addEventListener("click", e => {
          if (e.target === overlay) {
            const modalId = overlay.id;
            const type = modalId.replace("Modal", "");
            hideModal(type);
          }
        });
      });

      // 显示视口信息
      function updateViewportInfo() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        document.getElementById("viewport").textContent = `${width} × ${height}px`;
      }

      updateViewportInfo();
      window.addEventListener("resize", updateViewportInfo);

      // 按钮交互效果
      document.querySelectorAll(".btn, .demo-button").forEach(btn => {
        btn.addEventListener("click", function () {
          this.style.transform = "scale(0.98)";
          setTimeout(() => {
            this.style.transform = "";
          }, 150);
        });
      });

      // 输入框焦点效果
      document.querySelectorAll(".form-input").forEach(input => {
        input.addEventListener("focus", function () {
          this.style.transform = "scale(1.01)";
        });

        input.addEventListener("blur", function () {
          this.style.transform = "";
        });
      });

      console.log("✅ 移动端弹窗修复演示页面已加载");
      console.log("📱 请在不同设备尺寸下测试弹窗效果");
    </script>
  </body>
</html>
